
CREATE TABLE IF NOT EXISTS `testimonial` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `internalName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `customFields<PERSON>son` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;


CREATE TABLE IF NOT EXISTS `testimonial_localization` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mutationId` int(11) NOT NULL,
  `testimonial` int(11) NOT NULL,
  `name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameAnchor` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `nameTitle` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `keywords` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL,
  `isTop` int(11) NOT NULL DEFAULT 0,
  `public` int(11) NOT NULL DEFAULT 0,
  `forceNoIndex` int(11) NOT NULL DEFAULT 0,
  `hideInSearch` int(11) NOT NULL DEFAULT 0,
  `hideInSitemap` int(11) NOT NULL DEFAULT 0,
  `publicFrom` datetime DEFAULT NULL,
  `publicTo` datetime DEFAULT NULL,
  `edited` int(11) DEFAULT NULL,
  `editedTime` datetime DEFAULT NULL,
  `customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentSchemeJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
  `viewsNumber` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK_blog_mutation` (`mutationId`) USING BTREE,
  KEY `FK_blog_localization_blog` (`testimonial`) USING BTREE,
  CONSTRAINT `testimonial_localization_ibfk_1` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE,
  CONSTRAINT `testimonial_localization_ibfk_2` FOREIGN KEY (`testimonial`) REFERENCES `testimonial` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;

