{"name": "sedlakova-legal", "version": "1.0.0", "sideEffects": false, "browserslist": ["since 2020", "not < 0.01%", "not QQAndroid > 1"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@babel/core": "7.19.1", "@babel/plugin-transform-runtime": "7.19.1", "@babel/preset-env": "7.19.1", "@superkoders/eslint-config": "2.1.1", "@superkoders/prettier-config": "0.2.6", "@superkoders/stylelint-config": "2.2.2", "ansi-colors": "4.1.3", "autoprefixer": "10.4.12", "babel-loader": "8.2.5", "babel-loader-exclude-node-modules-except": "1.2.1", "browser-sync": "2.27.10", "cheerio": "1.0.0-rc.12", "deepmerge": "4.2.2", "del": "6.0.0", "eslint": "8.24.0", "fancy-log": "2.0.0", "gulp": "4.0.2", "gulp-cheerio": "1.0.0", "gulp-consolidate": "^0.2.0", "gulp-format-html": "1.2.5", "gulp-imagemin": "8.0.0", "gulp-plumber": "^1.2.1", "gulp-postcss": "9.0.1", "gulp-rename": "2.0.0", "gulp-sass": "^5.1.0", "gulp-svgmin": "4.1.0", "gulp-svgstore": "9.0.0", "gulp-twing": "4.0.0", "gulp-w3c-html-validator": "5.1.3", "gulp-zip": "5.1.0", "husky": "7.0.4", "import-fresh": "3.3.0", "lint-staged": "12.3.7", "lodash": "4.17.21", "node-notifier": "10.0.1", "parse-sass-value": "2.3.0", "postcss": "8.4.16", "sass": "1.55.0", "through2": "4.0.2", "twing": "5.1.2", "vinyl": "2.2.1", "webpack": "5.74.0"}, "dependencies": {"@babel/runtime": "7.27.1", "@floating-ui/dom": "1.7.0", "@hotwired/stimulus": "3.2.2", "@superkoders/cookie": "2.4.3", "@superkoders/modal": "2.1.1", "@superkoders/sk-tools": "1.8.3", "@vimeo/player": "2.26.0", "embla-carousel": "8.6.0", "embla-carousel-auto-scroll": "8.6.0", "embla-carousel-autoplay": "8.6.0", "gsap": "3.13.0", "libphonenumber-js": "1.12.7", "lite-vimeo-embed": "0.3.0", "lite-youtube-embed": "0.3.3", "naja": "3.2.1", "nette-forms": "3.5.1", "nouislider": "15.8.1", "stimulus-use": "0.52.3", "wnumb": "1.2.0", "yt-player": "3.6.1"}, "scripts": {"preversion": "npm run build", "version": "npx conventional-changelog-cli -p angular -i CHANGELOG.md -s -r 1 && git add -A", "postversion": "git push --follow-tags", "export": "npx gulp export", "build": "npx gulp min", "dev": "npx gulp", "prestart": "npm install", "preinstall": "npx check-engine", "start": "npm run dev", "lint:css": "stylelint \"src/**/*.{css,scss}\" || exit 0", "lint:css:fix": "prettier --write \"src/**/*.{css,scss}\" && stylelint --fix \"src/**/*.{css,scss}\"", "lint:js": "eslint . || exit 0", "lint:js:fix": "eslint . --fix", "print-version": "echo $npm_package_version", "snippet:build": "npx gulp build", "prepare": "husky install"}, "volta": {"node": "16.14.0", "npm": "8.6.0"}}