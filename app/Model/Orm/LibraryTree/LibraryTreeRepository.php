<?php

declare(strict_types=1);

namespace App\Model\Orm\LibraryTree;

/**
 * @method LibraryTree getById($id)
 * @method LibraryTree|null getBy(array $conditions)
 */
final class LibraryTreeRepository extends \Nextras\Orm\Repository\Repository
{
	static function getEntityClassNames(): array
	{
		return [LibraryTree::class];
	}

	public function getByUid(string $uid): ?LibraryTree
	{
		return $this->getBy(['uid' => $uid]);
	}
}
