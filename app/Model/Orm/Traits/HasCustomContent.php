<?php declare(strict_types = 1);

namespace App\Model\Orm\Traits;

use Nette\Utils\ArrayHash;
use Nette\Utils\Json;
use stdClass;
use App\Model\CustomContent\CustomContent;

trait HasCustomContent
{
	use HasCache;

	private CustomContent $customContent;

	public function injectCustomContent(CustomContent $customContent): void
	{
		$this->customContent = $customContent;
	}


	public function getCcSchemeJson(): string
	{
		// If scheme is empty, Json::encode returns '[]' which is incorrect
		if ($this->getCcScheme() === []) {
			return '{}';
		}

		return Json::encode($this->getCcScheme());
	}


	public function getCcScheme(): array
	{
		$components = $this->customContent->resolveCustomComponentsFor($this);
		$componentsByTemplate = [];
		foreach ($components as $component) {
			$componentsByTemplate[$component->template] = $component;
		}

		$scheme = [];
		$ccJson = $this->customContentJson;
		foreach (\array_keys((array) $ccJson) as $key) {
			$delimiterPosition = \strpos($key, '____');
			\assert($delimiterPosition !== false);
			$templateName = \substr($key, 0, $delimiterPosition);
			if (isset($componentsByTemplate[$templateName])) {
				$scheme[$key] = $componentsByTemplate[$templateName]->toArray()['scheme'];
			}
		}

		return $scheme;
	}


	public function getCcModulesJson(): string
	{
		if (!isset($this->cache['ccModulesJson'])) {
			$this->cache['ccModulesJson'] = Json::encode($this->customContent->resolveCustomComponentsFor($this));
		}

		return $this->cache['ccModulesJson'];
	}


	public function getCcModules(): array
	{
		if (!isset($this->cache['ccModules'])) {
			$this->cache['ccModules'] = $this->customContent->resolveCustomComponentsFor($this);
		}

		return $this->cache['ccModules'];
	}


	public function getCcJson(): string
	{
		$customContent = $this->customContentJson;

		return Json::encode($this->customContent->prepareForToShowInRs($this, $customContent));
	}


	protected function getterCc(): stdClass
	{
		assert($this->getMetadata()->hasProperty('customContentJson'));
		if (!isset($this->cache['customContent'])) {
			$customContent = $this->customContentJson;

			$this->cache['customContent'] = $this->customContent->prepareForToShow($this, $customContent);
		}

		return $this->cache['customContent'];
	}

	public function setCc(ArrayHash $customContent): void
	{
		$this->customContentJson = $customContent;
	}

	protected function setterCc(ArrayHash $customContent): void
	{
		unset($this->cache['customContent']);
		assert($this->getMetadata()->hasProperty('customContentJson'));
		$this->customContentJson = $customContent;
	}


	public function getEsContent(): string
	{
		$content = '';
		foreach ($this->cc as $item) {
			foreach ($item as $parts) {
				if ($parts instanceof stdClass) {
					foreach ((array) $parts as $part) {
						if (is_string($part)) {
							$content .= ' ' . strip_tags($part);
						}
					}
				}
			}
		}

		return $content;
	}

}
