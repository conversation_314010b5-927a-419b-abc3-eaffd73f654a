<?php declare(strict_types=1);

namespace App\Model\Orm\Log;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class LogMapper extends DbalMapper
{
	use HasCamelCase;
	protected $tableName = 'log';


	public function findForDataGrid(): ICollection
	{
		$builder = $this->builder();
		$builder->orWhere('message in %s[]', ['careerForm-form', 'contactForm-form']);
		$builder->orWhere('message like %_like_', 'customContentRenderer-serviceForm');
		return $this->toCollection($builder);
	}
}
