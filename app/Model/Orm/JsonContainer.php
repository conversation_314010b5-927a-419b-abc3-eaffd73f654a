<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nette\Utils\Json;
use Nette\Utils\JsonException;
use Nextras\Orm\Entity\ImmutableValuePropertyWrapper;

final class JsonContainer extends ImmutableValuePropertyWrapper
{

	public function setInjectedValue($value): bool
	{
		// array and object normalization
		$this->value = Json::decode(Json::encode($value));
		return true;
	}


	public function convertFromRawValue($value = null)
	{
		if (!isset($value) || !$value) {
			return Json::decode('{}');
		}

		return Json::decode($value);
	}

	/**
	 * @throws JsonException
	 */
	public function convertToRawValue($value)
	{
		return Json::encode($value);
	}

}
