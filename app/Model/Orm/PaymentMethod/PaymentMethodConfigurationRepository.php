<?php

declare(strict_types=1);

namespace App\Model\Orm\PaymentMethod;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method PaymentMethodConfiguration[]|ICollection getAvailable(Mutation $mutation, State $state, PriceLevel $priceLevel)
 * @method PaymentMethodConfiguration[]|ICollection getAvailableByDelivery(DeliveryMethodConfiguration $deliveryMethodConfiguration, Mutation $mutation, State $state, PriceLevel $priceLevel)
 */
final class PaymentMethodConfigurationRepository extends Repository
{
	public static function getEntityClassNames(): array
	{
		return [PaymentMethodConfiguration::class];
	}
}
