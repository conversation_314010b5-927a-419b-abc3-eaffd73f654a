<?php

declare(strict_types=1);

namespace App\Model\Orm\State;

use App\Model\Orm\Orm;
use App\Model\Orm\Traits\HasCache;

final class StateModel
{
	use HasCache;

	public function __construct(
		private readonly Orm $orm,
	)
	{
	}


	/**
	 * Vrati hodnoty vsech sazeb DPH pro vsechny publikovane staty jako strukturovane pole
	 * @return array<int, VatRates>
	 */
	public function getAllVatRatesValues(): array
	{
		if (!isset($this->cache['allVatRatesValues'])) {

			$states = $this->orm->state->findBy([
				'public' => 1
			])->fetchAll();

			$vatRatesList = [];
			foreach ($states as $state) {
				$vatRatesList[$state->id] = $state->vatRates;
			}

			$this->cache['allVatRatesValues'] = $vatRatesList;
		}

		return $this->cache['allVatRatesValues'];
	}

}
