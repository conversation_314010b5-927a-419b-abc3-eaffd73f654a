<?php declare(strict_types = 1);

namespace App\Model\Orm\EsIndex;

use App\Model\ConfigService;
use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasConsts;
use Elastica\Index;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use App\Model\Orm\JsonArrayHashContainer;

/**
 * @property int $id {primary}
 * @property string $type {enum self::TYPE_*}
 * @property string $name {default ''}
 * @property DateTimeImmutable $createdTime
 * @property DateTimeImmutable|null $startTime
 * @property DateTimeImmutable|null $finishTime
 * @property int|null $recreate {default 0}
 * @property string|null $status
 * @property ArrayHash $errorDetail {container JsonArrayHashContainer}
 * @property int $errorCount {default 0}
 * @property int|null $active {default 0}
 *
 *
 * RELATIONS
 *
 * @property Mutation $mutation {m:1 Mutation::$esIndexes}
 *
 *
 * VIRTUAL
 * @property-read string $createdTimeFormated {virtual}
 * @property-read Index $index {virtual}
 * @property-read string $esName {virtual}
 * @property-read ArrayHash $errorDetailItems {virtual}
 */
class EsIndex extends Entity
{

	use HasConsts;

	public const TYPE_ALL = 'all';
	public const TYPE_PRODUCT = 'product';
	public const TYPE_COMMON = 'common';

	private IndexModel $indexModel;

	public function injectElasticSearchIndexModel(IndexModel $indexModel): void
	{
		$this->indexModel = $indexModel;
	}

	protected function getterCreatedTimeFormated(): string
	{
		return $this->createdTime->format('Y-m-d_H-i-s');
	}


	public static function getUncommonTypes(): array
	{
		$types = self::getConstsByPrefix('TYPE_');
		unset($types[self::TYPE_ALL]);
		return $types;
	}


	protected function getterEsName(): string
	{
		return $this->name . '_' . $this->createdTimeFormated;
	}

	protected function getterIndex(): Index
	{
		return $this->indexModel->getIndex($this);
	}


	protected function getterErrorDetailItems(): ArrayHash
	{
		return $this->errorDetail->items ?? new ArrayHash();
	}

}
