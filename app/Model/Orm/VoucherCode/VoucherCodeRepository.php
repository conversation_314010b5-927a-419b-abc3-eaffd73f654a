<?php

declare(strict_types=1);

namespace App\Model\Orm\VoucherCode;

use App\Model\Orm\Mutation\Mutation;
use Nextras\Orm\Repository\Repository;

final class VoucherCodeRepository extends Repository
{
	static function getEntityClassNames(): array
	{
		return [VoucherCode::class];
	}

	public function getByCode(string $code, Mutation $mutation): ?VoucherCode
	{
		return $this->getBy([
			'code' => strtoupper(trim($code)),
			'voucher->mutation->id' => $mutation->id
		]);
	}

}
