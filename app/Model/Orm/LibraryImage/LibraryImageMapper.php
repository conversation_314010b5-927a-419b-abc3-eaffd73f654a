<?php declare(strict_types = 1);

namespace App\Model\Orm\LibraryImage;

use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class LibraryImageMapper extends <PERSON><PERSON><PERSON>ap<PERSON>
{

	use HasCamelCase;

	protected $tableName = 'image';

	public function findByFilter(array $filterData, ?LibraryTree $libraryTree): ICollection
	{
		$builder = $this->builder();

		if (isset($filterData['fulltext'])) {
			$builder->andWhere('( name LIKE %_like_ or alts LIKE %_like_)', $filterData['fulltext'], $filterData['fulltext']);
		}

		if (isset($filterData['directorySearch']) && $filterData['directorySearch']) {
			if ($libraryTree !== null) {
				$builder->andWhere('libraryId = %i', $libraryTree->id);
			}
		}

		return $this->toCollection($builder);
	}

	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()->select('i.*')->from('image', 'i')
			->andWhere('i.id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(i.id, ' . implode(',', $ids) . ')');
		return $this->toCollection($builder);
	}


	public function fixSortForDirectory(LibraryTree $libraryTree): void
	{
		$images = $this->builder()->select('i.*')->from($this->tableName, 'i')
			->andWhere('i.libraryId = %i', $libraryTree->id)
			->addOrderBy('sort asc');

		$results = $this->connection->queryByQueryBuilder($images);
		$sort = 10;
		foreach ($results as $image) {
			$this->connection->query('UPDATE image set sort = %i where id = %i', $sort, $image->id);
			$sort = $sort + 10;
		}
	}


}
