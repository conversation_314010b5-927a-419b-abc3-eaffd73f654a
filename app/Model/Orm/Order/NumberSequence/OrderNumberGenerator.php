<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\NumberSequence;

use App\Exceptions\LogicException;
use App\Model\Orm\Order\Order;

final readonly class OrderNumberGenerator
{
	public function __construct(
		private OrderNumberSequence $sequence,
		private OrderNumberFormatter $formatter,
	) {}

	public function generateOrderNumber(Order $order): string
	{
		$mutation = $order->mutation;
		$date = $order->placedAt;

		if ($order->placedAt === null) {
			throw new LogicException('Cannot generate number for draft order.');
		}

		$number = $this->sequence->getNextNumber($mutation, $date);
		return $this->formatter->format($number, $mutation, $date);
	}
}
