<?php

declare(strict_types=1);

namespace App\Model\Orm\Order;

/**
 * Decorators are meant to apply project-specific rules to the draft order, such as bulk discounts for specific
 * products, free delivery from a certain threshold, etc. Decorators should be applied to the draft order on every
 * request, and add, update, or remove discounts and other order items.
 */
interface OrderDecorator
{
	public function decorate(Order $order): void;
}
