<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductImage;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\QueryException;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class ProductImageMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'product_image';

	/**
	 * @throws QueryException
	 */
	public function setVariants(int $id, array $variants): Result
	{
		$sVariants = implode('|', $variants);
		return $this->connection->query(
			'UPDATE product_image SET variants=%s WHERE id = %i',
			$sVariants,
			$id
		);
	}

}
