<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariantPrice;

use App\Model\Orm\Price;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductVariant\ProductVariant;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property int $productId zatim neni treba relace
 * @property Price $price {embeddable}
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$productVariantPrices} {default 1}
 * @property ProductVariant $productVariant {m:1 ProductVariant::$prices}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$prices}
 */
class ProductVariantPrice extends Entity
{

	public const PRICE_ROUND_PRECISION = 5;

}
