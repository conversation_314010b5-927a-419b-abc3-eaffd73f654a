<?php declare(strict_types = 1);

namespace App\Model\Orm\ApiToken;

use App\Model\Orm\User\User;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use function sprintf;

/**
 * @property int $id {primary}
 * @property string $token
 * @property string $description
 * @property User $issuer {m:1 User, oneSided=true}
 * @property DateTimeImmutable $issuedAt
 * @property DateTimeImmutable|null $expiresAt
 * @property DateTimeImmutable|null $revokedAt
 * @property string $scope
 */
class ApiToken extends Entity
{

	public const SCOPE_ALL = 'all';

	public function __construct(
		User $user,
		string $description,
		DateTimeImmutable|null $expiresAt,
	)
	{
		parent::__construct();
		$this->token = sprintf('SA-AT-%s', Random::generate(42, '0-9a-zA-Z'));
		$this->description = $description;
		$this->issuer = $user;
		$this->issuedAt = new DateTimeImmutable();
		$this->expiresAt = $expiresAt;
		$this->revokedAt = null;
		$this->scope = self::SCOPE_ALL;
	}

	public function isValid(): bool
	{
		return $this->revokedAt === null
			&& ($this->expiresAt === null
				|| $this->expiresAt > new DateTimeImmutable()
			);
	}

	public function revoke(): void
	{
		if ($this->revokedAt === null) {
			$this->revokedAt = new DateTimeImmutable();
		}
	}

}
