<?php

declare(strict_types=1);

namespace App\Model\Orm\PriceLevel;

use Nextras\Orm\Repository\Repository;

/**
 * @method PriceLevel|null getById($id)
 */
final class PriceLevelRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [PriceLevel::class];
	}

	public function getDefault(): PriceLevel|null
	{
		return $this->getById(PriceLevel::DEFAULT_ID);
	}

}
