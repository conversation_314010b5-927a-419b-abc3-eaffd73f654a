<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Repository\IRepository;

class Creator
{

	public static function createNewEntity(IRepository $repository, string $class): IEntity
	{
		$newEntity = new $class();
		assert($newEntity instanceof IEntity);
		$repository->attach($newEntity);

		return $newEntity;
	}


	public static function createNewFilledEntity(IEntity $sourceEntity): IEntity
	{
		$repository = $sourceEntity->getRepository();
		$newEntity = self::createNewEntity($repository, $sourceEntity::class);
		$newEntity = CopyMachine::makeShallowCopy($sourceEntity, $newEntity);
		$newEntity = CopyMachine::makeHasOneRelationCopy($sourceEntity, $newEntity);
		return $newEntity;
	}

}
