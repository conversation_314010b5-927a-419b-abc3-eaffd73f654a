<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\All\Consumer;

use App\Model\ElasticSearch\All\ConvertorProvider;
use App\Model\ElasticSearch\All\ElasticAll;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\All\Message\ReplaceAllMessage;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Feature\Model\Orm\FeatureLocalizationRepository;
use App\PostType\Material\Model\Orm\MaterialLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalizationRepository;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
class ReplaceAllConsumer extends Consumer
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Service $elasticService,
		private readonly ConvertorProvider $convertorProvider,
		private readonly ConsumerHelper $consumerHelper,
		AuthorLocalizationRepository $authorLocalizationRepository,
		TreeRepository $treeRepository,
		BlogTagLocalizationRepository $blogTagLocalizationRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductRepository $productRepository,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
		SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		TestimonialLocalizationRepository $testimonialLocalizationRepository,
		MaterialLocalizationRepository $materialLocalizationRepository,
		FeatureLocalizationRepository $featureLocalizationRepository,
		ReferenceLocalizationRepository $referenceLocalizationRepository,
	)
	{
		parent::__construct(
			$authorLocalizationRepository,
			$treeRepository,
			$blogTagLocalizationRepository,
			$blogLocalizationRepository,
			$productRepository,
			$seoLinkLocalizationRepository,
			$testimonialLocalizationRepository,
			$materialLocalizationRepository,
			$featureLocalizationRepository,
			$referenceLocalizationRepository,
		);
	}

	public function __invoke(ReplaceAllMessage $message): void
	{
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		try {
			$this->orm->setMutation($esIndex->mutation);
			$this->mutationHolder->setMutation($esIndex->mutation);

			$object = $this->getObjectByClass($message->getClass(), $message->getId());
			$convertors = array_map(function ($convertorClass) {
				return $this->convertorProvider->get($convertorClass);
			}, $message->getConvertors());

			$this->elasticService->replaceDoc($esIndex, new ElasticAll($object, $convertors));

			if (($signals = $message->getSignals()) !== []) {
				$this->consumerHelper->handleSignals($esIndex, $signals);
			}
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), $message->getClass());
		}
	}

}
