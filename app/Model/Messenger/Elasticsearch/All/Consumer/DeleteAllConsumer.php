<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\All\Consumer;

use App\Model\ElasticSearch\All\ElasticAll;
use App\Model\ElasticSearch\Service;
use App\Model\Messenger\Elasticsearch\All\Message\DeleteAllMessage;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Product\ProductRepository;
use App\PostType\Feature\Model\Orm\FeatureLocalizationRepository;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalizationRepository;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
class DeleteAllConsumer extends Consumer
{

	public function __construct(
		private EsIndexRepository $esIndexRepository,
		private Service $elasticService,
		private ConsumerHelper $consumerHelper,
		AuthorLocalizationRepository $authorLocalizationRepository,
		TreeRepository $treeRepository,
		BlogTagLocalizationRepository $blogTagLocalizationRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductRepository $productRepository,
		SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		TestimonialLocalizationRepository $testimonialLocalizationRepository,
		MaterialLocalizationRepository $materialLocalizationRepository,
		FeatureLocalizationRepository $featureLocalizationRepository,
		ReferenceLocalizationRepository $referenceLocalizationRepository,
	)
	{
		parent::__construct(
			$authorLocalizationRepository,
			$treeRepository,
			$blogTagLocalizationRepository,
			$blogLocalizationRepository,
			$productRepository,
			$seoLinkLocalizationRepository,
			$testimonialLocalizationRepository,
			$materialLocalizationRepository,
			$featureLocalizationRepository,
			$referenceLocalizationRepository,
		);
	}

	public function __invoke(DeleteAllMessage $message): void
	{
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		try {
			$object = $this->getObjectByClass($message->getClass(), $message->getId());

			$elasticAll = new ElasticAll($object, []);
			$this->elasticService->deleteDoc($esIndex, $elasticAll);

		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), $message->getClass());
		}
	}

}
