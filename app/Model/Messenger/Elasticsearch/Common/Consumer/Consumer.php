<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Common\Consumer;

use App\Model\ElasticSearch\Common\Convertor\ReferenceData;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Feature\Model\Orm\FeatureLocalizationRepository;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalizationRepository;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use LogicException;

abstract class Consumer
{

	public function __construct(
		private TreeRepository $treeRepository,
		private BlogLocalizationRepository $blogLocalizationRepository,
		private MaterialLocalizationRepository $materialLocalizationRepository,
		private AuthorLocalizationRepository $authorLocalizationRepository,
		private FeatureLocalizationRepository $featureLocalizationRepository,
		private ReferenceLocalizationRepository $referenceLocalizationRepository,
	)
	{
	}

	protected function getObjectByClass(string $class, int $objectId): ?object
	{
		$this->blogLocalizationRepository->setPublicOnly(false);
		$this->materialLocalizationRepository->setPublicOnly(false);
		$this->authorLocalizationRepository->setPublicOnly(false);
		$this->featureLocalizationRepository->setPublicOnly(false);
		$this->referenceLocalizationRepository->setPublicOnly(false);
		$this->treeRepository->setPublicOnly(false);

		return match ($class) {
			AuthorLocalization::class => $this->authorLocalizationRepository->getById($objectId),
			FeatureLocalization::class => $this->featureLocalizationRepository->getById($objectId),
			ReferenceLocalization::class => $this->referenceLocalizationRepository->getById($objectId),
			MaterialLocalization::class => $this->materialLocalizationRepository->getById($objectId),
			BlogLocalization::class => $this->blogLocalizationRepository->getById($objectId),
			Tree::class, CatalogTree::class, CommonTree::class=> $this->treeRepository->getById($objectId),
			default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
		};
	}

}
