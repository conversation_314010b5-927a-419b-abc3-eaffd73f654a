<?php declare(strict_types = 1);

namespace App\Model\Messenger\Elasticsearch\Common\Consumer;

use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Orm;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\Model\ElasticSearch\Common\ConvertorProvider;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Service;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Messenger\Elasticsearch\Common\Message\ReplaceCommonMessage;
use App\Model\Messenger\Elasticsearch\ConsumerHelper;
use App\PostType\Feature\Model\Orm\FeatureLocalizationRepository;
use App\PostType\Material\Model\Orm\MaterialLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use LogicException;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Messenger\Handler\MessageHandlerInterface;
use Throwable;

#[AsMessageHandler]
class ReplaceCommonConsumer extends Consumer
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		private readonly Service $elasticService,
		private readonly ConvertorProvider $convertorProvider,
		TreeRepository $treeRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		AuthorLocalizationRepository $authorLocalizationRepository,
		FeatureLocalizationRepository $featureLocalizationRepository,
		MaterialLocalizationRepository $materialLocalizationRepository,
		ReferenceLocalizationRepository $referenceLocalizationRepository,
		private readonly ConsumerHelper $consumerHelper,
		private readonly MutationHolder $mutationHolder,
		private readonly Orm $orm,
	)
	{
		parent::__construct(
			$treeRepository,
			$blogLocalizationRepository,
			$materialLocalizationRepository,
			$authorLocalizationRepository,
			$featureLocalizationRepository,
			$referenceLocalizationRepository,
		);
	}

	public function __invoke(ReplaceCommonMessage $message): void
	{
		$esIndex = $this->esIndexRepository->getById($message->getEsIndexId());

		if ($esIndex === null) {
			throw new LogicException(sprintf('EsIndex %s not found', $message->getEsIndexId()));
		}

		try {

			$this->orm->setMutation($esIndex->mutation);
			$this->mutationHolder->setMutation($esIndex->mutation);

			bd($message->getClass()." ".$message->getId());
			$object = $this->getObjectByClass($message->getClass(), $message->getId());
			$convertors = array_map(function ($convertorClass) {
				return $this->convertorProvider->get($convertorClass);
			}, $message->getConvertors());

			$elasticProduct = new ElasticCommon($object, $convertors);
			$this->elasticService->replaceDoc($esIndex, $elasticProduct);

			if (($signals = $message->getSignals()) !== []) {
				$this->consumerHelper->handleSignals($esIndex, $signals);
			}
		} catch (Throwable $e) {
			$this->consumerHelper->handleError($esIndex, $e, $message->getId(), $message->getClass());
		}
	}

}
