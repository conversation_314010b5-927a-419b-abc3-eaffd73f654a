<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\ElasticItem;

use App\Model\BucketFilter\QueryAggregation;
use App\Model\BucketFilter\QueryFilter;
use App\Model\Orm\Parameter\Parameter;
use Closure;
use Elastica\Aggregation\AbstractAggregation;
use Elastica\Aggregation\Filter;
use Elastica\Query\Term;
use Elastica\Query\Terms;
use Elastica\QueryBuilder;

class MultiValue extends DiscreteValues
{

	/**
	 * @phpstan-param  Closure(): array $getOptionFunction
	 * @phpstan-param  null|Closure(mixed): (int|bool|string) $typeInElasticEnforcer
	 */
	public function __construct(
		string $elasticKey,
		array $selectedValues,
		private readonly Closure $getOptionFunction,
		QueryFilter $queryFilter,
		?Closure $typeInElasticEnforcer = null,
		string $operatorBetweenValues = QueryAggregation::OPERATOR_OR,
		bool $filterEmptySelectedValues = false,
	)
	{
		parent::__construct($elasticKey, $selectedValues, $queryFilter, $typeInElasticEnforcer, $operatorBetweenValues, $filterEmptySelectedValues);
	}

	public function getAggregation(array $filterItems = [], bool $withFiltering = true): AbstractAggregation
	{
		$b = new QueryBuilder();

		if ($withFiltering) {
			$aggFilterBuilder = $this->queryFilter->newGet($filterItems, $this->elasticKey);
		} else {
			$aggFilterBuilder = $b->query()->bool();
		}

		$aggr = new Filter($this->getAggregationName());
		$aggr->setFilter($aggFilterBuilder);

		$b = new QueryBuilder();
		foreach ($this->getOptions() as $option) {
			$parameterValueId = $option->id;
			$valuesForAggregation = $this->selectedValues;
			$valuesForAggregation[$parameterValueId] = (string) $parameterValueId;

			$parameterValueAgg = new Filter((string) $parameterValueId);

			$query = $b->query()->bool();
			if ($this->operatorBetweenValues === QueryAggregation::OPERATOR_AND) {
				foreach ($valuesForAggregation as $valueForAggregation) {
					$term = (new Term())->setTerm($this->elasticPath, $this->enforcerElasticType($valueForAggregation));
					$query = $query->addMust($term);
				}
			} else {
				foreach ($this->selectedValues as $selectedValue) {
					$query->addMust((new Term())->setTerm($this->elasticPath, $selectedValue));
				}
				$query->addMust((new Term())->setTerm($this->elasticPath, $parameterValueId));
			}

			$parameterValueAgg->setFilter($query);

			$aggr->addAggregation($parameterValueAgg);
		}

		return $aggr;
	}


	public function getOptions(): array
	{
		$getOptionFunction = $this->getOptionFunction;
		return $getOptionFunction();
	}

}
