<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\PriceLevel\PriceLevelModel;
use App\Model\Orm\Product\Product;

class PriceData implements Convertor
{

	public function __construct(
		private PriceLevelModel $priceLevelModel,
	)
	{
	}

	public function convert(Product $product): array
	{
		$mutation = $product->getMutation();
		$data = [];

		$priceLevels = $this->priceLevelModel->getAllPriceLevel()->findBy(['type' => PriceLevel::TYPE_DEFAULT]);

		foreach ($mutation->states as $state) {
			foreach ($priceLevels as $priceLevel) {
				assert($priceLevel instanceof PriceLevel);
				$nonZeroPricesVat = [];

				$someVariantHasSupply = false;
				foreach ($product->activeVariants as $variant) {
					$someVariantHasSupply = $variant->isInStock;
					if ($someVariantHasSupply) {
						break;
					}
				}

				foreach ($product->activeVariants as $variant) {
					if ($variant->isInStock || !$someVariantHasSupply) {
						$pricesVat = $variant->priceVat($mutation, $priceLevel, $state);
						if ( ! $pricesVat->isZero()) {
							$nonZeroPricesVat[] = $pricesVat->getAmount()->toFloat();
						}
					}
				}

				$data['statePricesWithVat'][$state->code][$priceLevel->type] = [];
				if ($nonZeroPricesVat !== []) {
					$data['statePricesWithVat'][$state->code][$priceLevel->type] = $nonZeroPricesVat;
				} else {
					$data['statePricesWithVat'][$state->code][$priceLevel->type] = [0];
				}

				$data['isInDiscount'][$state->code][$priceLevel->type] = false;
			}
		}

		$productLocalization = $product->getLocalization($mutation);
		$data['isPublic'] = (bool) $productLocalization->public;

		return $data;
	}

}
