<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product\Convertor;

use App\Model\ElasticSearch\ConvertorHelper;
use App\Model\ElasticSearch\Product\Convertor;
use App\Model\Orm\Product\Product;
use Nette\Utils\Strings;

class BaseData implements Convertor
{

	public function __construct()
	{
	}

	public function convert(Product $product): array
	{
		$mutation = $product->getMutation();
		$productLocalization = $product->getLocalization($mutation);
		$data = [];

		if ($productLocalization !== null) {

			$data = [
				'nameSort' => $productLocalization->name,
				'name' => $productLocalization->name,
				'nameTitle' => $productLocalization->nameTitle,
				'nameAnchor' => $productLocalization->nameAnchor,
				'content' => ($productLocalization->content) ? strip_tags($productLocalization->content) : '',
				'annotation' => $productLocalization->annotation,
				'isPublic' => (bool) $productLocalization->public,
				'isOld' => (bool) $product->isOld,
				'isNew' => (bool) $product->isNew,
				'publicFrom' => ConvertorHelper::convertTime($product->publicFrom),
				'publicTo' => ConvertorHelper::convertTime($product->publicTo),
				'soldCount' => $product->soldCount,
				'reviewAverage' => $product->reviewAverage,
			];

			$variants = $product->variants->toCollection()->findBy([
				'variantLocalizations->active' => 1,
				'variantLocalizations->mutation' => $mutation,
			]);

			$data['eans'] = [];
			$data['codes'] = [];

			foreach ($variants as $variant) {
				if ($variant->ean) {
					$data['eans'][] = Strings::lower((string) $variant->ean);
				}

				if ($variant->code) {
					$data['codes'][] = Strings::lower((string) $variant->code);

				}
			}
		}

		return $data;
	}

}
