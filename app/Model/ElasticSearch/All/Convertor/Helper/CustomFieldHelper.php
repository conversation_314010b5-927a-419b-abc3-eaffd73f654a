<?php

namespace App\Model\ElasticSearch\All\Convertor\Helper;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\File\File;
use App\Model\Orm\LibraryImage\LibraryImage;
use App\Model\Orm\Product\Product;
use App\PostType\Core\Model\LocalizationEntity;
use Nextras\Orm\Collection\DbalCollection;
use function array_values;

class CustomFieldHelper
{
	public static function getImagesIds(object $object): array
	{
		$imageIds = [];
		$fileIds = [];
		$saveValues = function ($item) use (&$imageIds, &$fileIds) {
			if ($item instanceof LazyValue) {
				if ($item->getEntity() instanceof File) {
					$fileIds[$item->initialId()] = $item->initialId();
				} elseif ($item->getEntity() instanceof LibraryImage) {
					$imageIds[$item->initialId()] = $item->initialId();
				}
			} elseif ($item instanceof DbalCollection) {
				foreach ($item->fetchAll() as $suggestEntity) {
					if ($suggestEntity instanceof LibraryImage) {
						$imageIds[$suggestEntity->id] = $suggestEntity->id;
					} elseif ($suggestEntity instanceof File) {
						$fileIds[$suggestEntity->id] = $suggestEntity->id;
					}
				}
			}
		};

		if ($object instanceof Product) {
			foreach ($object->images as $image) {
				$imageIds[$image->libraryImage->id] = $image->libraryImage->id;
			}
			foreach ($object->files as $file) {
				$fileIds[$file->file->id] = $file->file->id;
			}
		}

		if (isset($object->cf)) {
			self::walkThrough($object->cf, $saveValues);
		}
		if (isset($object->cc)) {
			self::walkThrough($object->cc, $saveValues);
		}

		if ($object instanceof LocalizationEntity && isset($object->getParent()->cf)) {
			self::walkThrough($object->getParent()->cf, $saveValues);
		}
		return [array_values($imageIds), array_values($fileIds)];
	}

	/**
	 * @param mixed $item
	 * @param \Closure(LazyValue|DbalCollection): void $saveValues
	 */
	private static function walkThrough(mixed $item, \Closure $saveValues): void
	{
		if ($item instanceof \stdClass || is_array($item)) {
			foreach ((array) $item as $subItem) {
				self::walkThrough($subItem, $saveValues);
			}
		} elseif ($item instanceof LazyValue) {
			$saveValues($item);
		} elseif ($item instanceof DbalCollection) {
			$saveValues($item);
		}
	}
}
