<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All\Convertor;

use App\Model\ElasticSearch\All\Convertor\Helper\CustomFieldHelper;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\Model\ElasticSearch\All\Convertor;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Testimonial\Model\Orm\TestimonialLocalization;

class TestimonialData implements Convertor
{

	public function convert(object $object): array
	{
		assert($object instanceof TestimonialLocalization);
		$ret = [
			'id' => $object->id,
			'isSystemPage' => false,
			'type' => 'testimonial',
			'langCode' => $object->mutation->langCode,

			'name' => $object->name . ' (' . $object->getParent()->getInternalName() . ')',
			'nameTitle' => $object->nameTitle,
			'description' => $object->description,
			'annotation' => $object->annotation,
		];
		$ret['kind'] = 'testimonial';

		$ret['repository'] = $object->getRepository()::class;
		list($imageIds, $fileIds) = CustomFieldHelper::getImagesIds($object);
		$ret['imageIds'] = $imageIds;
		$ret['fileIds'] = $fileIds;

		return $ret;
	}

}
