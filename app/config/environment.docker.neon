includes:
	- environment.dev.neon

parameters:
	stageName: local

	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # de

	config:
		domainUrl: https://sedlakova.superkoders.test/

		elastica:
			host: sedlakova_es
			port: 9200

		mutations:
			cs:
				domain: sedlakova.superkoders.test
			en:
				domain: sedlakova.superkoders.test
	migrations:
		withDummyData: false

	database:
		host: sedlakova_db
		database: sedlakova
		user: sedlakova
		password: sedlakova

	redis:
		host: sedlakova_redis

http:
	proxy:
		- *********/8

mail:
	host: sedlakova_mailcatcher
	port: 1025
