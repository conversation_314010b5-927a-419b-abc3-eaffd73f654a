parameters:

	esBaseName: %config.projectName%_%stageName%

	config:
		elasticSearch:
			enabled: true
			pathToSynonyms: %config.APP_DIR%/../documents/es/synonyms/

			index:
				all:
					name: '%esBaseName%_all'
					enabled: true
				common:
					name: '%esBaseName%_common'
					enabled: true
				product:
					name: '%esBaseName%_product'
					enabled: true

elastica:
	debug: %debugMode%
	config:
		host: %config.elastica.host%
		port: 9200

services:

	- App\Model\ElasticSearch\ConfigurationHelper

	- App\Model\Orm\EsIndex\EsIndexModel(%config.elasticSearch.index%)
	- App\Model\Orm\EsIndex\EsIndexFacade

	- \App\Model\ElasticSearch\IndexModel(%esBaseName%)
	- \App\Model\ElasticSearch\Repository()

	- \App\Model\ElasticSearch\All\Repository
	- \App\Model\ElasticSearch\Common\Repository

	- App\Model\ElasticSearch\AliasModel(%config.elasticSearch.index%)



## new version
	- App\Model\ElasticSearch\Service

	# index of ALL
	- App\Model\ElasticSearch\All\Facade

	- App\Model\ElasticSearch\All\ConvertorProvider
	- App\Model\ElasticSearch\All\Convertor\TreeData
	- App\Model\ElasticSearch\All\Convertor\BlogData
	- App\Model\ElasticSearch\All\Convertor\BlogTagData
	- App\Model\ElasticSearch\All\Convertor\ReferenceData
	- App\Model\ElasticSearch\All\Convertor\ReferenceTagData
	- App\Model\ElasticSearch\All\Convertor\AuthorData
	- App\Model\ElasticSearch\All\Convertor\ProductData
	- App\Model\ElasticSearch\All\Convertor\SeoLinkData
	- App\Model\ElasticSearch\All\Convertor\TestimonialData
	- App\Model\ElasticSearch\All\Convertor\FeatureData
	- App\Model\ElasticSearch\All\Convertor\MaterialData

	# index of COMMON
	- App\Model\ElasticSearch\Common\Facade
	- App\Model\ElasticSearch\Common\ResultReader
	- App\Model\ElasticSearch\Common\ConvertorProvider

	- App\Model\ElasticSearch\Common\Convertor\TreeData
	- App\Model\ElasticSearch\Common\Convertor\BlogData
	- App\Model\ElasticSearch\Common\Convertor\MaterialData
	- App\Model\ElasticSearch\Common\Convertor\FeatureData
	- App\Model\ElasticSearch\Common\Convertor\ReferenceData
	- App\Model\ElasticSearch\Common\Convertor\AuthorData


	#index of PRODUCT
	- App\Model\ElasticSearch\Product\ConvertorProvider
	- App\Model\ElasticSearch\Product\Facade
	- App\Model\ElasticSearch\Product\ResultReader

	- App\Model\ElasticSearch\Product\Convertor\BaseData
	- App\Model\ElasticSearch\Product\Convertor\CategoryData
	- App\Model\ElasticSearch\Product\Convertor\ParameterData
	- App\Model\ElasticSearch\Product\Convertor\StoreData
	- App\Model\ElasticSearch\Product\Convertor\PriceData
	- App\Model\ElasticSearch\Product\Convertor\TopScoreData


extensions:
	elastica: Contributte\Elastica\DI\ElasticaExtension
