monolog:
	channel:
		default: # default channel is required
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/syslog.log, 30, Monolog\Level::Warning)
				- Monolog\Handler\PsrHandler(Tracy\Bridges\Psr\TracyToPsrLoggerAdapter())
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor()

		formDataSaver:
			handlers:
				- App\Model\Monolog\Handler\DbalHandler
			processors:
				- Monolog\Processor\WebProcessor
				- App\Model\Monolog\Processor\FormDataProcessor(::realpath(%appDir%/../documents/fileLog))

		messenger:
			handlers:
				- Monolog\Handler\RotatingFileHandler(%appDir%/../nettelog/SymfonyMessenger/default.log, 30, Monolog\Level::Info)
			processors:
				- Monolog\Processor\MemoryPeakUsageProcessor
	hook:
		fromTracy: false # enabled by default, log through <PERSON> into Monolog
		toTracy: false # enabled by default, log through Monolog into Tracy
	manager:
		enabled: true # disabled by default
