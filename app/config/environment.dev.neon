parameters:
	stageName: 'dev'

	admin:
		allowedIpRanges:
			- "127.0.0.1"
			- "::1"

	config:
		isDev: true
		env: local

		esBaseName: %config.projectName%_%stageName%
		domainUrl: 'https://superadmin-dev.www6.superkoderi.cz/'
		domainUrlPdf: ''
		debuggerEditor: 'phpstorm://open?file=%file&line=%line'
		translations:
			insertNew: false
			markUsage: false

		dbalLog: false # vypis sql dotazu do souboru nettelog/mysql.log

		adminMail: '<EMAIL>'

		googleAnalyticsCode: "GTM-SUPERADMIN"
		googleAnalyticsName: "auto"

		mutations:
			cs:
				domain: superadmin-dev.www6.superkoderi.cz
				urlPrefix: false
				mutationId: 1
				rootId: 1
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				robots: "noindex, nofollow"
				sitemap: true

			en:
				domain: superadmin-dev.www6.superkoderi.cz
				urlPrefix: en
				mutationId: 2
				rootId: 2
				hidePageId: 425
				systemPageId: 423
				internalName: anglicky
				orderEmail: <EMAIL>
				robots: "noindex, nofollow"
				sitemap: false


	migrations:
		withDummyData: true

services:
	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")
#			- setTempDirectory("") # odkomentované => vypnuti cache u latte

	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')


session:
	expiration: 14 days
	cookie_secure: false

tracy:
#	maxDepth: 4
	bar:
		- Nette\Bridges\HttpTracy\SessionPanel

console.cache:
	cleaners:
		netteCaching: Contributte\Console\Extra\Cache\Cleaners\NetteCachingStorageCleaner

#application:
#	catchExceptions: true

