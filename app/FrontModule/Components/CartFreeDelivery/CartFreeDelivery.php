<?php declare(strict_types = 1);

namespace App\FrontModule\Components\CartFreeDelivery;

use App\FrontModule\Presenters\BasePresenter;

use App\Model\Orm\DeliveryMethod\DeliveryMethodConfigurationRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\ShoppingCart\ShoppingCart;
use App\Model\TranslatorDB;
use Brick\Money\Money;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;


/**
 * @property-read DefaultTemplate $template
 * @property-read BasePresenter $presenter
 */
final class CartFreeDelivery extends UI\Control {

	private ?Money $freeDeliveryAmount = null;

	public function __construct(
		private readonly Mutation $mutation,
		private readonly State $state,
		private readonly PriceLevel $priceLevel,
		private readonly ShoppingCart $shoppingCart,
		private readonly TranslatorDB $translator,
		private readonly DeliveryMethodConfigurationRepository $deliveryMethodConfigurationRepository,
	)
	{
		$this->onAnchor[] = $this->init(...);
	}

	private function init(): void
	{
		$this->freeDeliveryAmount = $this->deliveryMethodConfigurationRepository->getFreeDeliveryAmount($this->mutation, $this->state, $this->priceLevel);
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->freeDeliveryAmount = $this->freeDeliveryAmount;
		$this->template->show = $this->freeDeliveryAmount !== null;
		if ($this->template->show) {
			$this->template->cartAmount  = $this->shoppingCart->getTotalPriceVat();
			$this->template->deltaAmount = $this->freeDeliveryAmount->minus($this->template->cartAmount);
			$this->template->isFree = $this->template->deltaAmount->isGreaterThan(0);
		}

		$this->template->render(__DIR__ . '/cartFreeDelivery.latte');
	}
}
