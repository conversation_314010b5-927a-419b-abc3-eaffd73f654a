<?php declare(strict_types=1);

namespace App\FrontModule\Components;

use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Http\IRequest;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\ArrayHash;
use Nette\Utils\Random;

trait HasAntispamInput
{
	private SessionSection $sessionSection;
	private IRequest $httpRequest;
	private AntispamSession $antispamSession;

	public function injectSession(
		Session $session,
		IRequest $httpRequest,
		AntispamSession $antispamSession,
	): void
	{
		$this->sessionSection = $session->getSection(AntispamSession::SESSION_NAME);
		$this->httpRequest = $httpRequest;
		$this->antispamSession = $antispamSession;
	}


	protected function attachAntispamTo(UI\Form $form): void
	{
		$form->onValidate[] = $this->validateAntispamInForm(...); // @phpstan-ignore-line

		if (!isset($this->sessionSection->antispam['hash'])) {
			$this->antispamSession->prepareAntispam();
		}

		$form->addHidden('antispamHash', $this->sessionSection->antispam['hash'])->setRequired();
		$noJsValue = TranslatorDB::ALREADY_TRANSLATED_MARKER . $this->translator->translate('please_rewrite_value') . ' <strong data-antispam-target="source">' . $this->sessionSection->antispam['noJsValue'] . '</strong>';
		$form->addText('antispamNoJs', $noJsValue)->setHtmlAttribute('data-value', $this->sessionSection->antispam['noJsValue'])->setRequired();
	}


	private function validateAntispamInForm(UI\Form $form, ArrayHash $values): bool
	{
		if ($this->antispamValidate($values)) { // validační podmínka
			return true;
		} else {
			$form->addError('message_antispam_error');
			$form['antispamNoJs']->addError('message_antispam_error_no_js');
			return false;
		}
	}


	private function antispamValidate(ArrayHash $values): bool
	{
		return (
			isset($this->sessionSection->antispam['noJsValue'])
			&& isset($values->antispamNoJs)
			&& $this->sessionSection->antispam['noJsValue'] == $values->antispamNoJs
		);

	}

//{*ANTISPAM*}
//<p class="u-js-hidex js-antispam{if $form['antispamNoJs']->hasErrors()} has-error{/if}">
//	<label n:name="antispamNoJs" class="js-antispamText">
//		{_$form['antispamNoJs']->caption|noescape} {if $form['antispamNoJs']->isRequired()}*{/if}
//	</label>
//	<span class="inp-fix">
//		<input n:name="antispamNoJs" class="inp-text js-antispamValue">
//	</span>
//</p>
//{*/ANTISPAM*}


}
