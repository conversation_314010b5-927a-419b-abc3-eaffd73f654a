{var $props = [
	templateDirectory: (isset($props['templateDirectory'])) ? $props['templateDirectory'] : $defaultTemplateDirectory,
	customContent: (isset($props['customContent'])) ? $props['customContent'] : $defaultObjectCC,
]}

{if !Nette\Utils\Strings::endsWith($props['templateDirectory'], '/')}
	{var $props['templateDirectory'] = $props['templateDirectory'].'/'}
{/if}

{php $templateCounters = []}
{foreach $props['customContent'] as $key=>$item}
	{var $item = $item[0]} {*remove first level of group*}
	{var $templateName = substr($key, 0, strpos($key, '____'))}
	{var $niceName = ($allCustomComponentsLabels[$templateName] ?? null) ?? $templateName}

	{if !isset($templateCounters[$templateName])}
		{php $templateCounters[$templateName] = 0}
	{else}
		{php $templateCounters[$templateName]++}
	{/if}

	{var $id = $templateName . '_cc_' . $templateCounters[$templateName]}

	{embed $templates.'/part/core/component.latte', id: $id, name: $niceName, isDemo: $isDemo, user: $user}
		{block content}
			{if $isDev}
				{include $props['templateDirectory'].$templateName.'.latte', id: $id, customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$templateCounters[$templateName]}
			{else}
				{try}
					{include $props['templateDirectory'].$templateName.'.latte', id: $id, customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$templateCounters[$templateName]}
				{/try}
			{/if}
		{/block}
	{/embed}

	{* <div n:tag-if="$isDemo || $isAdmin" class="component u-mb-last-0">
		<span n:if="$isDemo || $isAdmin" class="component__title u-font-label"><b>ID:</b> {$id}, <b>Name:</b> {$niceName}</span>

		{if $isDev}
			{include $props['templateDirectory'].$templateName.'.latte', id: $id, customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter}
		{else}
			{try}
				{include $props['templateDirectory'].$templateName.'.latte', id: $id, customContentItem=>$item, parentIterator=>$iterator, sameTemplateCounter=>$sameTemplateCounter}
			{/try}
		{/if}
	</div> *}
{/foreach}
