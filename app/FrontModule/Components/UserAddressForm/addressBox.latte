{default $nameSuffix = ''}

<h2 class="h3 u-mb-sm">
	{_'title_personal_info'}
</h2>
{include '../inp.latte', form: $form, name: 'inv_firstname'.$nameSuffix, required: true, validate: true}
{include '../inp.latte', form: $form, name: 'inv_lastname'.$nameSuffix, required: true, validate: true}
{include '../inp.latte', form: $form, name: 'inv_phone'.$nameSuffix, type: 'tel'}
{include '../inp.latte', form: $form, name: 'inv_street'.$nameSuffix, required: true, validate: true}
{include '../inp.latte', form: $form, name: 'inv_city'.$nameSuffix, required: true, validate: true}
{include '../inp.latte', form: $form, name: 'inv_zip'.$nameSuffix, required: true, validate: true}
{include '../inp.latte', form: $form, name: 'inv_state'.$nameSuffix, validate: true}

<div n:class="f-open, $form['companyTab' . $nameSuffix]->value ? is-open" data-controller="toggle-class">
	<p>
		<label class="inp-item inp-item--checkbox">
			{var $controlName = 'companyTab'.$nameSuffix}
			<input n:name="$controlName" value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
			<span class="inp-item__text">
				{_'title_company_info'}
			</span>
		</label>
	</p>
	<div n:class="f-open__box">
		{include '../inp.latte', form: $form, name: 'inv_company' . $nameSuffix, validate: true, required: true, validate: true}
		{include '../inp.latte', form: $form, name: 'inv_ic' . $nameSuffix, validate: true, required: true, validate: true}
		{include '../inp.latte', form: $form, name: 'inv_dic' . $nameSuffix, validate: true}
	</div>
</div>

<div n:class="f-open, $form['deliveryTab' . $nameSuffix]->value ? is-open" data-controller="toggle-class">
	<p>
		<label class="inp-item inp-item--checkbox">
			{var $controlName = 'deliveryTab'.$nameSuffix}
			<input n:name="$controlName" value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
			<span class="inp-item__text">
				{_'title_delivery_address'}
			</span>
		</label>
	</p>
	<div n:class="f-open__box">
		<h2 class="h3 u-mb-sm">
			{_'title_delivery_address'}
		</h2>
		{include '../inp.latte', form: $form, name: 'del_firstname' . $nameSuffix, validate: true, required: true, validate: true}
		{include '../inp.latte', form: $form, name: 'del_lastname' . $nameSuffix, validate: true, required: true, validate: true}
		{include '../inp.latte', form: $form, name: 'del_company' . $nameSuffix, validate: true, required: true, validate: true}
		{include '../inp.latte', form: $form, name: 'del_phone' . $nameSuffix, type: 'tel', validate: true}
		{include '../inp.latte', form: $form, name: 'del_street' . $nameSuffix, validate: true, required: true}
		{include '../inp.latte', form: $form, name: 'del_city' . $nameSuffix, validate: true, required: true}
		{include '../inp.latte', form: $form, name: 'del_zip' . $nameSuffix, validate: true, required: true}
		{include '../inp.latte', form: $form, name: 'del_state' . $nameSuffix}
	</div>
</div>
