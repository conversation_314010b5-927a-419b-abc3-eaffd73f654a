{varType App\Model\Orm\Product\Product $product}


{var $hasPrice = $product->productAvailability->hasPrice($mutation, $priceLevel, $state)}
{var $showCart = $product->productAvailability->isShowCartCatalog($mutation, $priceLevel, $state)}

{if $showCart && $hasPrice}
	<form n:name="form" data-naja data-naja-loader="body" data-naja-modal-target data-naja-modal="snippet--precart" data-naja-modal-class="b-modal--prebasket" data-naja-history="off">
		<input class="u-vhide" n:name="quantity" data-step="1">
		<button n:name="send" class="btn link-mask__unmask">
			<span class="btn__text">
				{_"btn_add_to_basket"}
			</span>
		</button>
	</form>
{elseif $product->isInStock && $hasPrice}
	<p>
		<a href="{plink $product}" class="btn link-mask__unmask">
			<span class="btn__text">
				{_"btn_choose_variant"}
			</span>
		</a>
	</p>
{else}
	<p>
		<a href="{plink $product}" class="btn link-mask__unmask">
			<span class="btn__text">
				{_"btn_product_detail"}
			</span>
		</a>
	</p>
{/if}
