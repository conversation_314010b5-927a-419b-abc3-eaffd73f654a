{include $templates.'/part/box/steps.latte', class: 'u-pt-sm u-mb-sm', currentStep: 3}

<div class="u-mb-lg">
	{snippet step2Content}
		{var $showAddressForm = $user->isLoggedIn() || ($userStatus !== null && in_array($userStatus, [App\FrontModule\Components\CartUserDetail\UserStatus::WithoutSignIn, App\FrontModule\Components\CartUserDetail\UserStatus::WithoutRegistration, App\FrontModule\Components\CartUserDetail\UserStatus::Registered]))}
		{if !$showAddressForm}
			<div class="grid">
				<div class="grid__cell size--8-12@lg">
					{if !$user->isLoggedIn()}
						<div class="u-maw-4-12">
							{if $userStatus === null}{* email *}
								{include 'parts/emailForm.latte'}
							{elseif $userStatus == App\FrontModule\Components\CartUserDetail\UserStatus::Unknown}{* registrace *}
								{include 'parts/unknownForm.latte'}
							{elseif $userStatus == App\FrontModule\Components\CartUserDetail\UserStatus::Known}{* login *}
								{include 'parts/knownForm.latte'}
							{/if}
						</div>
					{/if}
				</div>
				<div class="grid__cell size--4-12@lg">
					{include parts/summary.latte}
				</div>
			</div>
		{else}
			{* FA/dodaci adresy *}
			<form n:name="addressForm" class="f-basket" novalidate="novalidate" data-naja data-naja-loader="body" data-naja-force-redirect data-naja-history="off">
				<div class="grid">
					<div class="grid__cell size--8-12@lg">
						{include 'parts/addressForm.latte', form: $form}
					</div>
					<div class="grid__cell size--4-12@lg">
						{include parts/summary.latte, form: $form}
					</div>
				</div>
			</form>
		{/if}
	{/snippet}
</div>


