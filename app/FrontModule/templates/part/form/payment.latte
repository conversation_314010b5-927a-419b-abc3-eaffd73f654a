{default $class = false}

<div n:class="f-method, $class">
	<ul class="f-method__list">
		{foreach $form['paymentMethod']->items as $item}
			{varType App\Model\Orm\PaymentMethod\PaymentMethodConfiguration $item}
			{embed $templates.'/part/form/part/method-item.latte',
				class:false,
				name:'paymentMethod',
				id: $item->id,
				title: $item->name,
				imgs: $item->cf->deliveryPayment??->icon ?? [],
				tooltipContent:$item->tooltip,
				price: $item->priceVat($priceLevel, $state, $shoppingCart)
			}
				{block description}
					{$item->desc}
				{/block}
			{/embed}
		{/foreach}
	</ul>
</div>


{* imgs:['/static/img/illust/sample.jpg'], *}
<div n:class="f-method, $class" n:if="$form['paymentMethod']->items === []">
	{_'payment_for_delivery_not_set'}
</div>
