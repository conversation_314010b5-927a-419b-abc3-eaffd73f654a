{default $class = false}
{var $title = $item->name ?? false}
{var $image = isset($item->blog->cf->base->mainImage) ? $item->blog->cf->base->mainImage->getEntity() ?? false : false}
{var $link = $item->alias->alias ?? false}

<article n:class="b-casestudy, $class, link-mask">
	<p class="u-mb-0 b-casestudy__img">
		{if $image}
			{php $img = $image->getSize('sm')}
			<img class="img img--4-3" src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}" loading="lazy">
		{else}
			<img class="img img--4-3" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
		{/if}
	</p>
	<div class="b-casestudy__main">
		<h3 n:if="$title" class="h6 u-mb-sm">
			{$title}
		</h3>
		<p n:if="$link" class="b-casestudy__title u-mt-0 u-mb-0">
			<a class="b-casestudy__link link-mask__link" href="{$link}">{_"btn_detail"} <span class="u-fw-n">&rarr;</span></a>
		</p>
	</div>
</article>
