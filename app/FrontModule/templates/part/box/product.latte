{default $class = false}
{default $titleTag = 'h3'}

{var $variant = null}

<article n:if="$product" n:class="b-product, $class, link-mask">
	{php $link = $presenter->link($product)}
	{var $activeVariant = $product->activeVariants->fetchAll()}
	{if count($activeVariant) == 1}
		{var $variant = $activeVariant[0]}
	{/if}

	<p class="b-product__img img u-mb-sm">
		{if $product->firstImage}
			{php $img = $product->firstImage->getSize('md')}
			<img src="{$img->src}" alt="" loading="lazy" width="{$img->width}" height="{$img->height}">
		{else}
			<img src="/static/img/illust/noimg.svg" alt="" loading="lazy" width="500" height="500">
		{/if}
		{include $templates.'/part/core/flags.latte', class=>'b-product__flags'}
		{include $templates.'/part/core/discount.latte', class=>'b-product__discount'}
	</p>

	<div class="b-product__content u-mb-last-0">
		<h3 n:tag="$titleTag" class="b-product__title h4">
			<a href="{$link}" class="b-product__link link-mask__link">
				{$product->nameAnchor}
			</a>
		</h3>

		<p class="b-product__desc" n:if="$product->annotation">
			{$product->annotation|texy|noescape}
		</p>

		<p class="b-product__price">
			{varType App\Model\Orm\Product\Product $product}
			{var $priceVat = $product->priceVat($mutation, $priceLevel, $state)}
			{var $price = $product->price($mutation, $priceLevel, $state)}
			{var $isPriceFrom = $product->hasPriceFrom($mutation, $priceLevel, $state)}

			{if $priceVat !== false}
				{if $priceVat->isZero()}
					{_price_not_determined}
				{else}
					<small n:if="$isPriceFrom">
						{_'price_from'}
					</small>
					<strong>
						{$priceVat|money}
					</strong>
					{_'price_tax'}<br>
					<small>
						{$price|money} {_price_without_tax}
					</small>
				{/if}
			{/if}
		</p>

		<p class="b-product__availability">
			{if $product->isOld}
				<strong>
					{_'availability_unavailable'}
				</strong>
			{elseif $product->notSoldSeparately}
				<strong>
					{_'not_sold_separately'}
				</strong>
			{elseif $product->isInPrepare}
				<strong>
					{_'presale_start'}
				</strong>
			{elseif $product->isInStock}
				<strong>
					{_'availability_in_stock'}
				</strong>
				<small n:if="!$variant && $product->supplyInfo->someNotAvailable">({_'availability_only_some_variants'})</small>
			{else}
				<strong>
					{_'availability_soldout'}
				</strong>
			{/if}
		</p>

		<div class="b-product__btn u-mb-last-0">
			{var $addToCart = $presenter['addToCart-'.$product->id]}
			{control $addToCart}
		</div>
	</div>

</article>
