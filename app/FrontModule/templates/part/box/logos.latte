{default $class = 'u-bgc-default u-pt-lg u-pb-lg'}
{default $id = 'logos_cf'}
{default $cf = $object->cf?->logos ?? []}
{default $images = $cf->images ?? []}

{embed $templates.'/part/core/component.latte', id: $id, user: $user}
	{block content}
		<div n:attr="id: $id" n:if="count($images)" n:class="b-logos, $class">
			<div class="embla" data-controller="embla" data-embla-auto-scroll-value='{"speed": 0.5}' data-embla-settings-value='{"loop": true}'>
				<div class="embla__viewport" data-embla-target="viewport">
					<ul class="b-logos__list embla__container">
						<li n:foreach="$images as $image" class="b-logos__item">
							{php $img = $image->getSize('xs')}
							<img src="{$img->src}" alt="" width="{$img->width}" height="{$img->height}">
						</li>
					</ul>
				</div>
			</div>
		</div>
	{/block}
{/embed}
