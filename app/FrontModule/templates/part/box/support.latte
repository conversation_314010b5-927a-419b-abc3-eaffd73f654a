{default $class = 'u-bgc-tertiary u-pt-xl u-pb-xl u-pt-3xl@lg u-pb-3xl@lg'}
{default $id = 'support_cf'}
{default $cf = $object->cf??->support ?? false}
{default $button = $object->cf?->support->button ?? false}
{default $title = $object->cf?->support->title ?? false}
{default $content = $object->cf?->support->content ?? false}
{default $image = $object->cf?->support->image ?? false}
{default $hasAnimation = $object->cf?->support->hasAnimation ?? false}

{embed $templates.'/part/core/component.latte', id: $id, user: $user}
	{block content}
		<section n:attr="id: $id" n:if="$cf && $title && $content" n:class="b-support, $class">
			<div class="row-main">
				<div class="grid grid--middle grid--x-3xl">
					<div class="grid__cell size--5-12@lg">
						<h2 n:if="$title" class="h4">
							{$title}
						</h2>
						<div n:if="$content" class="b-support__content">
							{$content|noescape}
						</div>
						{if $button}
							{include $templates . '/part/core/linkChoose.latte', item: $button, class: "btn btn--bd", isButton: true, hasArrow: true}
						{/if}
					</div>
					<div class="grid__cell size--7-12@lg">
						<div class="b-animation" data-controller="animation">
							<img class="b-animation__img" src="/static/img/illust/graf.png" alt="" loading="lazy" width="757" height="305">
							<svg viewBox="0 0 768 309" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 0; left: 0;">
								<path data-animation-target="path" d="M1 234C337.96 210.103 460.107 205.124 756 1" fill="none" stroke="transparent" stroke-width="1" />
							</svg>
							<span class="b-animation__svg" data-animation-target="rocket">
								{('rocket')|icon}
							</span>
						</div>
					</div>
				</div>
			</div>
		</section>
	{/block}
{/embed}
