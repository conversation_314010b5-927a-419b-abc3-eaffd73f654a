{default $class = ''}
{default $name = $seoLink??->name ?? $object->name}
{default $annotation = $seoLink??->description ?? $object??->annotation ?? null}
{default $cf = $object->cf->annot_career ?? false}

<header n:class="b-career, $class">
	<div class="row-main row-main--author">
		<div class="b-career__cnt u-ta-c u-maw-sm u-mx-auto">
			<h1 class="b-career__title">
				{$name|texy|noescape}
			</h1>
			<div n:ifcontent class="u-mb-last-0 b-career__desc">
				<p n:if="$annotation">
					{rtrim($annotation ?? '', '<br>')|texy|noescape}
				</p>
				{block annot}{/block}
			</div>
		</div>

		<div n:if="isset($cf->images) && $cf->images" class="b-career__images">
			<div class="b-career__image" n:foreach="$cf->images as $image">
				{php $img = $image->getSize('md-3-5')}
				{if $img}
					<img class="img img--3-5" src="{$img->src}" alt="{$image->name}" width="{$img->width}" height="{$img->height}" fetchpriority="high" loading="eager">
				{else}
					<img class="img img--3-5" src="/static/img/illust/noimg.svg" alt="" fetchpriority="high" loading="eager">
				{/if}
			</div>
		</div>
	</div>
</header>
