{default $class = 'u-mb-md u-mb-xl@md'}
{default $id = false}
{default $title = false}
{default $items = []}

{embed $templates.'/part/core/component.latte', id: $id, user: $user}
	{block content}
		<div n:attr="id: $id" n:if="count($items)" n:class="b-highlights, $class">
			<div class="row-main">
				<h2 n:if="$title" class="b-highlights__title u-ta-c h4 u-mb-md">{$title}</h2>
				<ul class="b-highlights__list grid grid--center">
					<li n:foreach="$items as $item" class="b-highlights__item grid__cell size--6-12@md size--4-12@lg">
						<div class="b-highlights__box link-mask u-mb-last-0">
							{php $image = isset($item->image) ? $item->image->getEntity() ?? false : false}
							<h3 n:tag="$title ? 'h3' : 'h2'" n:if="$item->name ?? false" class="tag h6">
								<img n:if="$image" src="{$image->getSize('xs')->src}" alt="" class="tag__icon" loading="lazy">
								{$item->name}
							</h3>
							<p n:if="$item->text">
								{$item->text|texy|noescape}
							</p>
							{php $link = $item->link ?? false}
							{if $link}
								{include $templates.'/part/core/linkChoose.latte', item=>$link, class=>'b-highlights__link', hasArrow=>true, class=>'btn link-mask__link', isButton=>true}
							{/if}
						</div>
					</li>
				</ul>
			</div>
		</div>
	{/block}
{/embed}
