{varType App\Model\Pages $pages}
{default $isCareer = isset($object->uid) && $object->uid === 'career' || isset($object->uid) && $object->uid === 'services' ?? false}

<header n:class="$isCareer ? 'header header--shadow' : 'header'">
	<div class="row-main">
		<div class="header__wrap" data-controller="toggle-class">
			<p class="header__logo">
				{if !$isHomepage && $pages->title !== null}
					<a n:href="$pages->title" class="header__link">
						{include 'logoSvg.latte', isHover: true}
					</a>
				{else}
					{include 'logoSvg.latte'}
				{/if}
			</p>
			<p class="header__search u-mb-0">
				<a n:href="$pages->search" class="btn btn--icon">
					<span class="btn__text">
						{('search')|icon, 'btn__icon'}
						<span class="u-vhide">{_btn_search}</span>
					</span>
				</a>
			</p>
			<p class="header__burger u-mb-0">
				<button type="button" class="b-burger" data-action="toggle-class#toggle">
					<span class="b-burger__inner">
						<span></span>
						<span></span>
						<span></span>
						<span></span>
					</span>
					<span class="u-vhide">{_"title_menu"}</span>
				</button>
			</p>
			<div class="header__menu">
				{control menu}
				<div class="header__menu-bg" data-action="click->toggle-class#removeClass"></div>
			</div>
		</div>
	</div>

</header>
