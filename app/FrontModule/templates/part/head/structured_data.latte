
{*
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "url": {$mutation->getBaseUrl()}
}
</script>
*}

{*Logo*}
<script type="application/ld+json">
{
	"@context": "https://schema.org",
	"@type": "LegalService",
	"name" : "SEDLAKOVA LEGAL",
  "description": "Právní služby pro technologické firmy, startupy a investory.",
	"url": {$mutation->getBaseUrl()},
	"logo": {$mutation->getBaseUrl()."/static/img/logo.png"},
	 "contactPoint": {
	    "@type": "ContactPoint",
	    "telephone": "+420 733 555 958",
	    "contactType": "customer service",
	    "areaServed": "CZ"
    },
		"address": {
			"@type": "PostalAddress",
			"streetAddress": "<PERSON><PERSON>š<PERSON><PERSON><PERSON> 123",
			"addressLocality": "<PERSON><PERSON>",
			"postalCode": "60200",
			"addressCountry": "CZ"
		}
}
</script>


{*breadcrump*}
{control breadcrumb:structuredData}


{*<script type="application/ld+json">*}
{*{*}
  {*"@context": "https://schema.org",*}
  {*"@type": "BreadcrumbList",*}
  {*"itemListElement": [{*}
    {*"@type": "ListItem",*}
    {*"position": 1,*}
    {*"name": "Books",*}
    {*"item": "https://example.com/books"*}
  {*},{*}
    {*"@type": "ListItem",*}
    {*"position": 2,*}
    {*"name": "Authors",*}
    {*"item": "https://example.com/books/authors"*}
  {*},{*}
    {*"@type": "ListItem",*}
    {*"position": 3,*}
    {*"name": "Ann Leckie",*}
    {*"item": "https://example.com/books/authors/annleckie"*}
  {*},{*}
    {*"@type": "ListItem",*}
    {*"position": 4,*}
    {*"name": "Ancillary Justice",*}
    {*"item": "https://example.com/books/authors/ancillaryjustice"*}
  {*}]*}
{*}*}
{*</script>*}




{if $object instanceOf App\Model\BlogLocalization}
	{control blogLocalizationStructuredData}
{/if}
{* review *}
{*TODO*}
{*<meta property="twitter:image" content="{$domainUrl|replace:'.cz/':'.cz'}{$img->src}" />*}
