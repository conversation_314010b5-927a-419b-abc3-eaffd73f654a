{default $class = "u-mb-md u-mb-lg@md"}
{default $id = 'features_cf'}
{default $name = false}
{default $annotation = false}
{* {default $name = $object->name ?? $object??->nameTitle ?? false} *}
{* {default $annotation = $object??->annotation ?? $seoLink??->description ?? false} *}
{* {default $titleTag = 'h1'} *}
{default $items = $object->crossroad ?? []}
{default $btn = false}
{default $btnItemClass = 'btn btn--bd'}
{* {default $pager = true} *}
{default $uidsToSkip = []}


{embed $templates.'/part/core/component.latte', id: $id, user: $user, name: $name}
	{block content}
		<section n:attr="id: $id" n:if="count($items) > 0" n:class="c-articles, $class">
			<div class="row-main">
				<div class="u-mb-last-0 u-mb-md">
					<h2 n:if="$name" class="c-articles__title u-mb-sm u-ta-c h4">
						{$name}
					</h2>
					<p n:if="$annotation" class="c-articles__desc u-mx-auto u-maw-c u-ta-c">
						{rtrim($annotation, '<br>')|texy|noescape}
					</p>
				</div>

				<div class="c-articles__list grid u-mt-md">
					<div n:foreach="$items as $item" n:if="(isset($item->uid) && !in_array($item->uid, $uidsToSkip)) || !isset($item->uid)" class="c-articles__item grid__cell size--6-12@md size--4-12@lg">
						{include '../box/feature.latte', titleTag: $name ? 'h3' : 'h2', article: $item, class: false, btnClass: $btnItemClass}
					</div>
					<div n:if="isset($object->cf->feature) && $object->cf->feature" class="c-articles__item grid__cell size--6-12@md size--4-12@lg">
						{default $title = $object->cf->feature->title ?? false}
						{default $annot = $object->cf->feature->annotation ?? false}
						{default $link = $object->cf->feature->link ?? false}
						{include '../box/feature.latte', article: true, name: $title, annotation: $annot, class: false, btnClass: $btnItemClass, link: $link}
					</div>
				</div>

				{if $btn}
					<p class="c-articles__btn u-ta-c u-pt-md u-pt-lg@md">
						{include $templates . '/part/core/linkChoose.latte', item: $btn, class: "btn", isButton: true, hasArrow: true}
					</p>
				{/if}

				{* {if $pager}
					{snippet articlesPagerBottom}
						{control pager, [class: 'u-pt-sm u-pt-md@md', showPages: false, showMoreBtn: true]}
					{/snippet}
				{/if} *}
			</div>
		</section>
	{/block}
{/embed}

