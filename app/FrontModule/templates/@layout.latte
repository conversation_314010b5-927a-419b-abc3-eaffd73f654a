<!DOCTYPE html>
<html lang="{$mutation->langCode}" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		{var $keywords = $seoLink??->keywords ?? $object->keywords}
		{if $keywords !== null}
			<meta name="keywords" content="{$keywords}">
		{/if}

		{var $description = $seoLink??->description ?? $object->description ?? $object->annotation}
		{if $description !== null}
			<meta name="description" content="{$description|texy:true}">
		{/if}

		{control robots}
		{control canonicalUrl}
		<meta name="viewport" content="width=device-width, initial-scale=1">

		<title n:snippet="title">
			{var $nameTitle = $seoLink??->nameTitle ?? $object->nameTitle}
			{if $nameTitle !== null}
				{$nameTitle}
			{/if}
			{if !$isHomepage} {* Přídavek za title, kter<PERSON> se dává jen pro ne homepage stránky *}
				| {_title}
			{/if}
		</title>

		{include 'part/head/style.latte', object=>$object}
		{include 'part/head/scripts.latte', object=>$object}


		{include 'part/head/meta.latte'}
		{include 'part/head/structured_data.latte'}

		{include 'part/tracking/googleAnalytics.latte' showTop=>TRUE}

		{var $scripts = [
			'/static/js/app.js?t=' . $webVersion
		] }
		{foreach $scripts as $script}
			<link rel="preload" as="script" href="{$script}">
		{/foreach}
		{*
			<link rel="dns-prefetch" href="https://www.google-analytics.com">
			<link rel="dns-prefetch" href="https://www.googletagmanager.com"> {/gtm.js}
			<link rel="preconnect" href="https://www.google.com" crossorigin>
			<link rel="preconnect" href="https://www.youtube.com" crossorigin>
			<link rel="preconnect" href="https://connect.facebook.net" crossorigin>
			<link rel="preconnect" href="https://static.doubleclick.net" crossorigin>
			<link rel="preconnect" href="https://client.crisp.chat" crossorigin>
		*}
	</head>
	<body data-controller="naja modal">
		{include 'part/tracking/googleAnalytics.latte' showBottom=>TRUE}
		{include 'part/menu/accessibility.latte'}

		{snippetArea header}
			{include 'part/header.latte'}
		{/snippetArea}

		<main id="main" class="main">
			{include #content}
		</main>

		{include './part/box/last-visited.latte'}

		{include 'part/footer.latte'}

		{if $mutation->isEnabledCookieModal}
			{include 'part/box/cookie.latte'}
		{/if}

		{control editButton}
		<div class="body-loader__loader"></div>

		{* {include 'part/head/speculation-rules.latte'} *}

		{foreach $scripts as $script}
			<script src="{$script}"></script>
		{/foreach}
		<script>
			App.run({
				apiKey: {$googleApiKey},
				assetsUrl: '/static/',
			});
		</script>
	</body>
</html>
