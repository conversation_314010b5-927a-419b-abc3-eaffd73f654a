<?php declare(strict_types=1);

namespace App\FrontModule\Presenters\Search;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\BucketFilter\BucketFilterFactory;
use App\Model\BucketFilter\Result;
use App\Model\BucketFilter\SetupCreator\Search\BasicElasticItemListFactory;
use App\Model\BucketFilter\SetupCreator\Search\BoxListFactory;
use App\Model\BucketFilter\SetupCreator\Search\ElasticItemListFactory;
use App\Model\BucketFilter\SortCreator;
use App\Model\ElasticSearch\Common\ElasticCommon;
use App\Model\ElasticSearch\Common\Repository;
use App\Model\ElasticSearch\Common\ResultReader;
use App\Model\Link\LinkSeo;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeMapper;
use App\PostType\Page\Model\Orm\TreeRepository;
use Elastica\Query\Term;
use Nette\Application\Attributes\Persistent;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Collection\ArrayCollection;
use Nextras\Orm\Collection\EmptyCollection;

/**
 * @property-read CommonTree $object
 */
final class SearchPresenter extends BasePresenter
{

	#[Persistent]
	public string $search;

	#[Persistent]
	private string $selectedTab = 'pages';

	private array $filterParams;


	public function __construct(
		private readonly ResultReader $commonResultReader,
		private readonly Repository $esCommonRepository,
		private readonly SortCreator $sortCreator,
		private readonly LinkSeo $linkSeo,

	)
	{
		parent::__construct();
	}

	protected function startup(): void
	{
		parent::startup();
		$this->setObject($this->params['object']);
	}


	public function actionDefault(CommonTree $object, ?string $search = null, bool $suggest = false, array $filter = [], ?string $selectedTab = null): void
	{
		$this->selectedTab = $selectedTab ?? $this->selectedTab;

		$this->setObject($object);
		$this->filterParams = $filter;

		if ($search === null) {
			$search = $this->request->getPost('search');
		}

		bd($search);
		if ($search === null) {
			$search = '';
		}
//		$this->search = $search;
		$this->search = trim((string)$search);

	}

	public function renderDefault(?string $search = null, bool $suggest = false): void
	{
		$sort = $this->sortCreator->create('score', $this->currentState, $this->priceLevel);

//		if ($search === null) {
//			$search = $this->request->getPost('search');
//		}

		$trees = new EmptyCollection();
//		$blogs = new EmptyCollection();

		$itemsObject = Result::from(
			new EmptyCollection(), /** @phpstan-ignore-line */
			0,
			0,
		);

		$this->template->hasResults = false;

		if ($search) {
			$search = trim($search);

			$mutation = $this->object->mutation;
			$this->template->search = $search;

			$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
			$this['pager']->object = $this->getObject();

			if (isset($this->params['more'])) {
				$this['pager']->setStartPage($this->params['more']);
			}

			$paginator = $this['pager']->getPaginator();
			$paginator->itemsPerPage = 3;

			$treeMust = [
				new Term(['public' => true]),
			];

			// hack
			$treeResultsFull = $this->esCommonRepository->fulltextSearch($mutation, $treeMust, $search);
			$paginator->itemCount = $treeResultsFull->count();
			bd($paginator->itemCount);

			$treeResults = $this->esCommonRepository->fulltextSearch($mutation, $treeMust, $search, $paginator->itemsPerPage, [], $paginator->offset);

			$allItemsArray = $this->commonResultReader->mapResultToEntityCollectionUniversal($treeResults, 10);

			$mapper = $this->orm->getRepositoryByName('tree');
			$trees = new ArrayCollection($allItemsArray, $mapper);   // máme ICollection
			bd($trees->count());
		}

		$this->template->hasResults = $trees->count(); //|| $blogs->count()
		$this->template->trees = $trees;
		$this->template->search = $this->search;
		$this->template->cleanFilterParam = $this->filterParams;
		$this->template->linkSeo = $this->linkSeo;

		if ($suggest) {
			$this->setView('suggest');
		}

		if ($this->isAjax()) {
			$this->redrawControl();
		}
	}


	public function handleSwitchTab(string $selectedTab): void
	{
		$this->selectedTab = $selectedTab;
	}

}
