{if $trees->count()}
	<div class="b-result u-maw-xs u-mx-auto u-mb-md">
		<h2 class="print h4 u-vhide u-mb-md">
			{_search_pages} ({$trees->count()})
		</h2>
		<div n:foreach="$trees as $tree" n:if="$tree->name ?? false" class="link-mask">
			{php $content = $tree->annotation ?? $tree->content ?? false}
			<h3 class="h5">{$tree->name}</h3>
			<div n:if="$tree" class="u-mb-md">
				<p n:if="$content" class="b-result__content">
					{$content}
				</p>
				<p class="u-ta-r">
					{if $tree instanceOf App\PostType\Blog\Model\Orm\BlogLocalization}
						{*je to BLOG*}
						<span class="btn btn--square btn--bd"><span class="btn__text">{_search_blog}</span></span>
					{elseif $tree instanceOf App\PostType\Material\Model\Orm\MaterialLocalization}
						{*je to akce,material*}
						<span class="btn btn--square btn--bd"><span class="btn__text">{_search_material}</span></span>
					{elseif $tree instanceOf App\PostType\Material\Model\Orm\AuthorLocalization}
						{*je to nekdo z tymu*}
						<span class="btn btn--square btn--bd"><span class="btn__text">{_search_people}</span></span>
					{elseif $tree instanceOf App\PostType\Material\Model\Orm\FeatureLocalization}
						{*je to SLUZBA*}
						<span class="btn btn--square btn--bd"><span class="btn__text">{_search_service}</span></span>
					{elseif $tree instanceOf App\PostType\Material\Model\Orm\ReferenceLocalization}
						{*je to REFERENCE*}
						<span class="btn btn--square btn--bd"><span class="btn__text">{_search_reference}</span></span>
					{elseif $tree instanceOf App\PostType\Page\Model\Orm\CommonTree}
						<span class="btn btn--square btn--bd"><span class="btn__text">{_search_page}</span></span>
						{*je to strom*}
					{/if}

					<a n:href="$tree" class="btn btn--bd link-mask__link">
						<span class="btn__text">
							{_search_blogs_detail}<span class="btn__arrow u-fw-n">&rarr;</span>
						</span>
					</a>
				</p>
			</div>
		</div>
		{control pager, [class: 'u-pt-sm u-pt-md@md', showPages: false, showMoreBtn: true]}
	</div>
{/if}
