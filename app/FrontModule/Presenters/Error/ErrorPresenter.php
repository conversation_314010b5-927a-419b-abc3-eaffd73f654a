<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Error;

use App\FrontModule\Presenters\BasePresenter;
use App\Model\Orm\Mutation\MutationModel;
use App\Model\StaticPage\Page404Factory;
use App\Model\TranslatorDB;
use Nette;
use Nette\Application\Request;
use Throwable;
use Tracy\Debugger;

final class ErrorPresenter extends BasePresenter
{

	protected bool $bypassBasicAuth = true;

	public function __construct(
		private readonly MutationModel $mutationModel,
		private readonly Page404Factory $page404Factory,

	)
	{
		parent::__construct();
	}

	public function actionDefault(Throwable $exception, ?Request $request = null): void
	{
		// adhoc detect mutation
		$host = $this->getHttpRequest()->getUrl()->getHost();
		$urlPrefix = '';
		foreach ($this->configService->get('mutations') as $m) {
			if ($m['urlPrefix'] && strpos($_SERVER['REQUEST_URI'], $m['urlPrefix'] . '/') !== false) {
				$urlPrefix = $m['urlPrefix'];
				break;
			}
		}

		$hotsWithoutWWW = str_replace('www.', '', $host);
		$this->mutation = $this->mutationModel->findForRouter($hotsWithoutWWW, $urlPrefix);
		$this->orm->setMutation($this->mutation);
		$this->translator->reInit($this->mutation, false);
		$this->mutationHolder->setMutation($this->mutation);

		Debugger::enable(null, ROOT_DIR . '/nettelog');

		if ($this->isAjax()) { // AJAX request? Just note this error in payload.
			$this->payload->error = true;
			$this->terminate();

		} elseif ($exception instanceof Nette\Application\BadRequestException) {

			$name = $this->translator->translate('page_404_name');
			$annotation = $this->translator->translate('page_404_annotation');
			$description = $this->translator->translate('page_404_description');
			$object = $this->page404Factory->create($this->mutation, $name, $annotation, $description);

			$this->setObject($object);
			$this->template->object = $this->getObject();

			$code = $exception->getCode();
			// load template 403.latte or 404.latte or ... 4xx.latte
			$this->setView(in_array($code, [403, 404, 405, 410, 500], true) ? (string) $code : '4xx');
			// log to access.log
			$this->template->alias = $_SERVER['REQUEST_URI'];
			$this->template->message = $exception->getMessage();

			Debugger::log("HTTP code $code: {$exception->getMessage()} in {$exception->getFile()}:{$exception->getLine()}", 'access');

		} else {
			$this->setView('500'); // load template 500.latte
			Debugger::log($exception, Debugger::ERROR); // and log exception
		}
	}

}
