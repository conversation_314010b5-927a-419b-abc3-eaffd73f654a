{default $class = 'u-mb-sm'}
{default $sortOptions = ['cheapest', 'name']}

<div n:class="f-sort, $class">
	<ul class="f-sort__list grid">
		{foreach $sortOptions as $sort}
			{include #sortRow sort=>$sort}
		{/foreach}
	</ul>
</div>


{define #sortRow}
	{if ($catalogOrder ?? ($control->getParameter('order') ?? 'latest')) == $sort}
		<li class="f-sort__item grid__cell size--auto is-active">
			<span class="f-sort__link is-active">
				{_'sort_'.$sort}
			</span>
		</li>
	{else}
		{capture $link}{link 'this', pager-page => null, filter => $cleanFilterParam, order=>$sort}{/capture}
		{php $link = urldecode(htmlspecialchars_decode($link))}
		<li class="f-sort__item grid__cell size--auto">
			<a href="{$link}" class="f-sort__link" data-naja data-naja-loader="body" data-naja-history="off" {if $linkSeo->hasNofollow($object, ['filter' => $cleanFilterParam])}rel="nofollow"{/if}>
				{_'sort_'.$sort}
			</a>
		</li>
	{/if}
{/define}