<?php declare(strict_types = 1);

namespace App\AdminModule\Components\SynonymsForm;

use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use App\Model\ConfigService;
use App\Model\Translator;

/**
 * @property-read DefaultTemplate $template
 */
final class SynonymsForm extends UI\Control
{

	public function __construct(
		private readonly Mutation $mutation,
		private readonly EsIndexModel $esIndexModel,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
	) {}

	public function render(): void
	{
		$timestamp = $this->configService->get('elasticSearchConfig', ConfigService::ES_SYNONYMS_UPDATED);
		$timestampUsed = $this->configService->get('elasticSearchConfig', ConfigService::ES_USED_INDEX_TS);
		if ($timestamp === $timestampUsed) {
			$this->template->statusInfo = 'ok';
		} else {
			$this->template->statusInfo = 'waiting';
		}

		$this->template->setTranslator($this->translator);
		$this->template->items = $this->mutation->synonyms;
		$this->template->render(__DIR__ . '/synonymsForm.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);

		$form->addSubmit('send');
		$form->onSuccess[] = [$this, 'formSucceeded'];
		return $form;
	}

	public function formSucceeded(UI\Form $form): void
	{
		$originSynonyms = $this->mutation->synonyms;
		$values = $form->getHttpData();

		$synonyms = [];
		$words = [];
		$errors = [];

		foreach ($values['word'] as $k => $word) {
			$word = trim($word);

			if (empty($word)) {
				continue;
			}

			if (array_key_exists($word, $words)) { // duplicita slova
				$errors[] = $word;
				continue;
			}

			$wordSynonyms = $this->getSynonyms($values['synonyms'][$k]);

			if (empty($wordSynonyms)) { // syn vůbec nevyplněno
				continue;
			}

			foreach ($wordSynonyms as $n => $syn) {
				if (array_key_exists($syn, $synonyms)) { // duplicita synonyma
					unset($wordSynonyms[$n]);
					$errors[] = $syn;
					continue;
				}

				$synonyms[$syn] = $syn;
			}

			if (empty($wordSynonyms)) { // ze synonym už žádné nezbylo
				continue;
			}

			$words[$word] = $wordSynonyms;
		}

		foreach ($words as $word => $syns) {
			foreach ($syns as $m => $syn) {
				if (array_key_exists($syn, $words)) { // shoda synonyma se slovem
					unset($words[$word][$m]);
					$errors[] = $syn;

					if (count($words[$word]) < 1) { // u slova už nezbylo ani jedno synonymum
						unset($words[$word]);
					}
				}
			}
		}

		// fix array keys
		foreach ($words as $word => $synonyms) {
			$words[$word] = array_values($synonyms);
		}

		$oldSynonymJson = $this->mutation->getRawValue('synonyms');
		$this->mutation->synonyms = ArrayHash::from($words);
		$newSynonymJson = $this->mutation->getRawValue('synonyms');

		$changed = ($oldSynonymJson !== $newSynonymJson);

		$this->orm->persistAndFlush($this->mutation);

		if ($changed) {
			$esIndexesToRecreate = $this->orm->esIndex->findBy([
				'active' => 1,
				'mutation' => $this->mutation,
			]);

			foreach ($esIndexesToRecreate as $esIndexToRecreate) {
				$this->esIndexModel->markToRecreate($esIndexToRecreate);
			}
		}

		$this->flashMessage($this->translator->translate('msg_ok_synonyms'), 'ok');

		if ($errors) {
			$this->flashMessage($this->translator->translate('msg_info_synonyms_duplicity') . ': ' . implode(', ', $errors));
		}

		$this->presenter->redirect('this');
	}

	/**
	 * @param string $synonyms
	 * @return array
	 */
	private function getSynonyms(string $synonyms): array
	{
		$synonyms = explode(Mutation::SYNONYMS_DELIMITER, $synonyms);
		foreach ($synonyms as $k => $s) {
			$s = trim($s);
			if (empty($s)) {
				unset($synonyms[$k]);
				continue;
			}

			$synonyms[$k] = $s;
		}

		return $synonyms;
	}

}
