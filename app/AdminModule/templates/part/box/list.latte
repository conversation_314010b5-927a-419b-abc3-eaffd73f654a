{var $props = [
	items: $props['items'] ?? [],
	title: $props['title'] ?? '',
	dragdrop: $props['dragdrop'] ?? false,
	add: $props['add'] ?? false,
	file: $props['file'] ?? false,
	addData: $props['addData'] ?? [],
	classes: $props['classes'] ?? ['u-mb-sm'],
	data: $props['data'] ?? [],
	listData: $props['listData'] ?? [],
]}
{var $classes = implode(' ', array_merge(['b-list'], $props['classes']))}


<div
	class="{$classes}"
	{foreach $props['data'] as $key=>$value}
		data-{$key}="{$value|noescape}"
	{/foreach}
>
	{if $props['title']}
		<h2 class="b-std__title title">
			{$props['title']}
		</h2>
	{/if}
	<div
		class="b-list__list"
			{foreach $props['listData'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
	>
		{foreach $props['items'] as $item}
			{include $templates.'/part/box/list-item.latte',
				props => [
					texts: $item['texts'] ?? [],
					inps: $item['inps'] ?? [],
					img: $item['img'] ?? '',
					checkboxes: $item['checkboxes'] ?? [],
					btnsBefore: $item['btnsBefore'] ?? [],
					btnsAfter: $item['btnsAfter'] ?? [],
					tags: $item['tags'] ?? [],
					langs: $item['langs'] ?? [],
					data: $item['data'] ?? null,
					dragdrop: $props['dragdrop'],
				]
			}
		{/foreach}

	</div>
	{if $props['add']}
		<button
			type="button"
			class="b-list__add btn{if $props['file']} b-list__add--file{/if}"
			{foreach $props['addData'] as $key=>$value}
				data-{$key}="{$value|noescape}"
			{/foreach}
		>
			{include $templates.'/part/icons/plus.svg'}
			{if $props['file']}<input type="file" multiple data-action="change->List#addFile">{/if}
		</button>
	{/if}
</div>

