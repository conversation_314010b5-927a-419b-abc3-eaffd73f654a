<nav class="m-main">
	{foreach $menu as $section => $items}
		<h2 n:if="$section && !is_numeric($section)" class="m-main__title">
			{_$section}
		</h2>
		<ul n:ifcontent class="m-main__list" n:inner-foreach="$items as $i">
			{continueIf isset($i['devOnly']) && $i['devOnly'] === true && $userEntity->role !== 'developer'}

			<li n:class="m-main__item, isset($i['devOnly']) && $i['devOnly'] === true ? dev">
				{if $i['action'] == "Import:default" && ($userEntity->role == "developer" || $userEntity->role == "admin")}
					<a n:href="$i['action']" n:class="($presenter->name == $i['resource']) ? is-active, m-main__link, item-icon, isset($i['sub']) ? m-main__link--sub">
						<span class="item-icon__icon icon">
							{include '../icons/'.$i['icon'].'.svg'}
						</span>
						<span class="item-icon__text">
							{_$i['title']}
						</span>
					</a>
				{else}
					<a n:href="$i['action']" n:class="($presenter->name == $i['resource']) ? is-active, ($i['resource'] == 'Admin:Catalog' && $presenter->name == 'Admin:Product') ? is-active, m-main__link, item-icon, isset($i['sub']) ? m-main__link--sub">
						<span class="item-icon__icon icon">
							{include '../icons/'.$i['icon'].'.svg'}
						</span>
						<span class="item-icon__text">
							{_$i['title']}
							{if $i['action'] == "Order:default" && $count = $newOrders->count()}
								[<span class="u-color-primary">{$count}</span>]
							{/if}
						</span>
					</a>
				{/if}
			</li>
		</ul>
	{/foreach}
</nav>
