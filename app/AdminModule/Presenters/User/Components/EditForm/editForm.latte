{varType App\Model\Orm\User\User $object}

{snippet editForm}
	{form editForm  autocomplete => 'off', novalidate=>'novalidate'}
		<h1>{if $isEdit}{$object->name}{*$object->firstname} {$object->lastname*}{else}{_"Create new user"}{/if}</h1>
		<ul class="message message-error" n:if="$form->hasErrors()">
			{foreach $form->errors as $key=>$error}
				{breakIf $key > 0}
				<li>{_$error}</li>
			{/foreach}
		</ul>
		<div class="grid-row">
			<p class="grid-1-4">
				{label email /}<br>
				<span class="inp-fix">
					<input n:name="email" class="inp-text w-full{if $form['email']->hasErrors()} error{/if}">
				</span>
			</p>
			<p class="grid-1-4">
				{label role /}<br>
				<span class="inp-fix inp-fix-select">
					<select n:name="role" class="inp-text w-full{if $form['role']->hasErrors()} error{/if}">
					</select>
				</span>
			</p>
			<p class="grid-1-4">
				{label priceLevel /}<br>
				<span class="inp-fix inp-fix-select">
					<select n:name="priceLevel" class="inp-text w-full{if $form['priceLevel']->hasErrors()} error{/if}">
					</select>
				</span>
			</p>
			<p class="grid-1-4">
				{if $isEdit}
					<label>{_'label_mutation'}</label><br>
					<span class="icon icon-globe"></span>
					<span class=""><strong>{$object->mutation->name}</strong></span><br>

				{else}
					{label userMutations /}
					<span class="inp-fix  inp-fix-select">
						{input userMutations class => 'inp-text'}
					</span>
				{/if}
			</p>
		</div>
		<div class="grid-row">
			<p class="grid-1-4 reset">
				{label password /}<br>
				<span class="inp-fix">
					<input n:name="password" autocomplete="new-password" class="inp-text w-full{if $form['password']->hasErrors()} error{/if}">
				</span>
			</p>
			<p class="grid-1-4 reset">
				{label passwordVerify /}<br>
				<span class="inp-fix">
					<input n:name="passwordVerify" class="inp-text w-full{if $form['passwordVerify']->hasErrors()} error{/if}">
				</span>
			</p>
		</div>
		<div class="menu-tabs">
			<ul class="reset">
				<li><a href="#tab-personal">Osobní údaje</a></li>
				<li><a href="#tab-addresses">Adresy</a></li>
				<li><a href="#tab-companies">Firemní údaje</a></li>
				<li n:if="$object && $object->isPersisted() && $object->getCfScheme()"><a href="#tab-customfields">{_tab_customfields}</a></li>
{*				<li><a href="#tab-delivery">Dodací údaje</a></li>*}
				{*<li><a href="#tab-orders">Objednávky</a></li>*}
			</ul>
		</div>

		<div id="tab-personal" class="tab-fragment">
			<div class="grid-row">
				<p class="grid-1-4">
					{label firstname /}<br>
					<span class="inp-fix">
						<input n:name="firstname" class="inp-text w-full{if $form['firstname']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4">
					{label lastname /}<br>
					<span class="inp-fix">
						<input n:name="lastname" class="inp-text w-full{if $form['lastname']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4">
					{label phone /}<br>
					<span class="inp-fix">
						<input n:name="phone" class="inp-text w-full{if $form['phone']->hasErrors()} error{/if}">
					</span>
				</p>
			</div>
			<div class="grid-row">
				<p class="grid-1-4">
					{label street /}<br>
					<span class="inp-fix">
						<input n:name="street" class="inp-text w-full{if $form['street']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4">
					{label city /}<br>
					<span class="inp-fix">
						<input n:name="city" class="inp-text w-full{if $form['city']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-4 reset">
					{label zip /}<br>
					<span class="inp-fix">
						<input n:name="zip" class="inp-text w-full{if $form['zip']->hasErrors()} error{/if}">
					</span>
				</p>

				<p class="grid-1-4 reset">
					{label state /}<br>
					<span class="inp-fix inp-fix-select">
						<select n:name="state" class="inp-text w-full{if $form['state']->hasErrors()} error{/if}">
						</select>
					</span>
				</p>
			</div>
		</div>
		<div id="tab-addresses" class="tab-fragment" n:if="$isEdit">
			<div class="grid-row" n:if="$object->customAddress">
				<div n:foreach="$object->customAddress as $k => $i" class="grid-1-3">
					<h4>Fakturační</h4>
					<hr>
					{_'user_firstname'}: <strong>{$i->invFirstname}</strong><br>
					{_'user_lastname'}: <strong>{$i->invLastname}</strong><br>
					{_'phone'}: <strong>{$i->invPhone}</strong><br>
					{_'street'}: <strong>{$i->invStreet}</strong><br>
					{_'city'}: <strong>{$i->invCity}</strong><br>
					{_'zip'}: <strong>{$i->invZip}</strong><br>
					{_'state'}: <strong>{isset($states[$i->invState]) ? $states[$i->invState] : ''}</strong><br>
					<br>
					{_'company'}: <strong>{$i->invCompany}</strong><br>
					{_'company_id'}: <strong>{$i->invIc}</strong><br>
					{_'vat_number'}: <strong>{$i->invDic}</strong><br>

					<h4>Dodací</h4>
					<hr>
					{_'user_firstname'}: <strong>{$i->delFirstname}</strong><br>
					{_'user_lastname'}: <strong>{$i->delLastname}</strong><br>
					{_'company'}: <strong>{$i->delCompany}</strong><br>
					{_'phone'}: <strong>{$i->delPhone}</strong><br>
					{_'street'}: <strong>{$i->delStreet}</strong><br>
					{_'city'}: <strong>{$i->delCity}</strong><br>
					{_'zip'}: <strong>{$i->delZip}</strong><br>
					{_'state'}: <strong>{isset($states[$i->delState]) ? $states[$i->delState] : ''}</strong><br>
				</div>
			</div>
		</div>
		<div id="tab-companies" class="tab-fragment">
			<div class="message message-info">
				<p>{_msg_info_firm}</p>
			</div>
			<div class="grid-row">
				<p class="grid-1-2 reset">
					{label company /}<br>
					<span class="inp-fix">
						<input n:name="company" class="inp-text w-full{if $form['company']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-2 reset">
					{label ic /}<br>
					<span class="inp-fix">
						<input n:name="ic" class="inp-text w-full{if $form['ic']->hasErrors()} error{/if}">
					</span>
				</p>
				<p class="grid-1-2 reset">
					{label dic /}<br>
					<span class="inp-fix">
						<input n:name="dic" class="inp-text w-full{if $form['dic']->hasErrors()} error{/if}">
					</span>
				</p>
			</div>
		</div>
		{* @todo user future vypis dodacich customAddress
		<div id="tab-delivery" class="tab-fragment">
			<div class="message message-info">
				<p>{_msg_info_delivery}</p>
			</div>
			<div class="grid-row">

			</div>
		</div>
		*}




		<div id="tab-customfields" class="tab-fragment" n:if="$object  && $object->isPersisted() && $object->getCfScheme()">
			<div class="c-custom-fields">
				{var $mutation = $object->mutation}
				{var $langCode = $mutation->langCode}

				<div data-controller="CustomFields"
					 data-action="CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldFile:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
					 data-customfields-lang-value="{$langCode}"
					 data-customfields-scheme-value="{$object->getCfSchemeJson()}"
					 data-customfields-values-value='{$object->getCfContent()}'
					 data-customfields-uploadurl-value="{$fileUploadLink}"
					 data-customfields-mutationid-value="{$mutation->id}"

				>

					<div data-customfields-target="content"></div>
					<input type="hidden" data-customfields-target="values" name="customFields">
				</div>
			</div>
		</div>

		<div class="fixed-bar">
			<button class="btn btn-green btn-icon-before">
				<span><span class="icon icon-checkmark"></span> {_save_button}</span>
			</button>
			{if isset($object->id) && $object->id != $userEntity->id}
				<a n:href="delete" class="btn btn-red btn-icon-before btn-delete ajax">
					<span><span class="icon icon-close"></span> {_delete_button}</span>
				</a>
			{/if}
		</div>

	{/form}
{/snippet}
