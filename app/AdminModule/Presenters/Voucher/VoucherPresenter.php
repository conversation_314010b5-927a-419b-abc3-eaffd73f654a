<?php

namespace App\AdminModule\Presenters\Voucher;


use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\Voucher\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\Voucher\Components\DataGrid\DataGridFactory;
use App\AdminModule\Presenters\Voucher\Components\Form\Form;
use App\AdminModule\Presenters\Voucher\Components\Form\FormFactory;
use App\AdminModule\Presenters\Voucher\Components\ShellForm\ShellForm;
use App\AdminModule\Presenters\Voucher\Components\ShellForm\ShellFormFactory;
use App\Model\Orm\Voucher\Voucher;


class VoucherPresenter extends BasePresenter
{
	private ?Voucher $object;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly FormFactory $formFactory,
	)
	{
	}

	public function startup(): void
	{
		parent::startup();
	}



	public function actionEdit(int $id): void
	{
		$this->object = $this->orm->voucher->getById($id);

		if (!$this->object) {
			$this->redirect('default');
		}
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

	protected function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create($this->userEntity);
	}

	protected function createComponentForm(): Form
	{
		return $this->formFactory->create($this->object, $this->userEntity);
	}
}
