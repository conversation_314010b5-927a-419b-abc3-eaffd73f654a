<?php declare(strict_types=1);

namespace App\AdminModule\Presenters\PaymentMethod\Components\Form;


use App\Model\Orm\Orm;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PaymentMethod\PaymentMethodPrice;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\Form\Builder as CoreBuilder;
use Nette\Forms\Container;
use Nette\Http\Request;
use Nette\Application\UI;
use Nextras\Orm\Collection\ICollection;

final class Builder
{
	/** @var ICollection<PriceLevel> */
	private ICollection $priceLevels;

	public function __construct(
		private readonly Orm $orm,
		private readonly CoreBuilder $coreBuilder,
		private readonly Translator $translator,
		private readonly Request $request,
	)
	{
		$this->priceLevels = $this->orm->priceLevel->findAll();
	}

	public function build(\Nette\Application\UI\Form $form, PaymentMethodConfiguration $paymentMethodConfiguration, User $user): void
	{

		$this->addCommonToForm($form, $paymentMethodConfiguration);
		$this->addVats($form, $paymentMethodConfiguration);
		$this->addPrices($form, $paymentMethodConfiguration, $this->request->getPost());

		$this->coreBuilder->addButtons($form);
		$this->coreBuilder->addPublish($form, $paymentMethodConfiguration);
	}

	private function addCommonToForm(UI\Form $form, PaymentMethodConfiguration $paymentMethodConfiguration): void
	{
		$form->addText('name', 'name')
		     ->setRequired()
		     ->setDefaultValue($paymentMethodConfiguration->name);
		$form->addTextArea('desc', 'desc')
		     ->setDefaultValue($paymentMethodConfiguration->desc);
		$form->addTextArea('tooltip', 'tooltip')
		     ->setNullable()
		     ->setDefaultValue($paymentMethodConfiguration->tooltip);
		$form->addText('sort', 'sort')
		     ->addRule($form::INTEGER, 'Please enter valid number.')
		     ->setDefaultValue($paymentMethodConfiguration->sort);
		$form->addCheckbox('isRecommended', 'is_recommended')
		     ->setDefaultValue($paymentMethodConfiguration->isRecommended);

		$form->addHidden('cf');
	}
	private function addVats(UI\Form $form, PaymentMethodConfiguration $paymentMethodConfiguration): void
	{
		$vatsContainer = $form->addContainer('vats');

		/** @var State $state */
		foreach ($paymentMethodConfiguration->mutation->states as $state) {
			$stateContainer = $vatsContainer->addContainer($state->id);

			$vatRates = [];
			foreach ($state->vatRates as $level => $rate) {
				if ($rate !== null) {
					$vatRates[$level->value] = sprintf('%s%% (%s)', $rate, $this->translator->translate('vat_rate_' . $level->value));
				}
			}

			$stateId = $state->id;

			$stateContainer->addSelect('vatRate', $state->code, $vatRates)->setTranslator(null);
			if (isset($paymentMethodConfiguration->vats->$stateId)) {
				$stateContainer['vatRate']->setDefaultValue($paymentMethodConfiguration->vats->$stateId);
			}
		}
	}

	private function addPrices(UI\Form $form, PaymentMethodConfiguration $paymentMethodConfiguration, array $postData): void
	{
		$pricesContainer = $form->addContainer('prices');

		if ($postData === []) {
			/** @var PaymentMethodPrice $price */
			foreach ($paymentMethodConfiguration->prices as $price)
			{
				$this->createPriceContainer($paymentMethodConfiguration, $pricesContainer, $price->id, $price);
			}
		} elseif (isset($postData['prices'])) {
			foreach ($postData['prices'] as $priceId => $priceData) {
				if (is_int($priceId)) {
					$this->createPriceContainer($paymentMethodConfiguration,$pricesContainer, $priceId, $price ?? null);
				} elseif (preg_match('/^newItemMarker_/', $priceId)) {
					$price = $paymentMethodConfiguration->prices->toCollection()->getById($priceId);
					$this->createPriceContainer($paymentMethodConfiguration, $pricesContainer, $priceId, $price);
				}
			}
		}

		$this->createPriceContainer($paymentMethodConfiguration, $pricesContainer, 'newItemMarker');
	}

	private function createPriceContainer(PaymentMethodConfiguration $paymentMethodConfiguration, Container $pricesContainer, string|int $name, ?PaymentMethodPrice $price = null): void
	{
		$priceContainer = $pricesContainer->addContainer($name);

		$priceContainer->addSelect("priceLevel","price_level", $this->priceLevels->fetchPairs('id', 'name'))
		               ->setDefaultValue($price?->priceLevel->id ?? PriceLevel::DEFAULT_ID);//->setPrompt('choose');;
		$priceContainer->addSelect("state","state", $paymentMethodConfiguration->mutation->states->toCollection()->fetchPairs('id', 'name'))
		               ->setDefaultValue($price?->state->id);//->setPrompt('choose');;

		$priceContainer->addText('price', 'price')
		               ->setHtmlType('number')
		               ->setHtmlAttribute('step', 0.0001)
		               ->setDefaultValue($price?->price->asMoney()->getAmount()->toFloat() ?? 0);

		$priceContainer->addText('currency', 'currency')->setNullable()->setDefaultValue($price?->price->asMoney()->getCurrency()->getCurrencyCode() ?? null);


		if($name === 'newItemMarker') {
			$priceContainer['priceLevel']->setPrompt('choose');
			$priceContainer['state']->setPrompt('choose');
			$priceContainer['currency']->setValue('CZK');
		}

	}
}
