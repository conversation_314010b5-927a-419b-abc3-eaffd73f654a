{form form class => ''}

	<div class="grid-row">
		<p class="grid-1-4">
			{input id}

			{label name /}<br>
			<span class="inp-fix">
				{input name class => 'inp-text w-full'}
			</span>
		</p>
		<p class="grid-1-4">
			{label type /}<br>
			<span class="inp-fix">
				{input type class => 'inp-text w-full select-param-type'}
			</span>
		</p>

	</div>
	<div class="grid-row">
		<p class="grid-1-4">
			{label uid /}<br>
			<span class="inp-fix">
				{input uid class => 'inp-text w-full'}
			</span>
		</p>
		<p n:if="isset($form['extId'])" class="grid-1-4">
			{label extId /}<br>
			<span class="inp-fix">
				{input extId class => 'inp-text w-full'}
			</span>
		</p>
	</div>
	<div class="grid-row">
		<p class="grid-1-4 reset no-label box-param-type" data-type='["select", "multiselect", "number"]'>
			<span class="inp-item inp-center">
				{input isInFilter: value: 1}
				{label isInFilter: /}
			</span>
		</p>
		<p n:if="isset($form['isProtected'])" class="grid-1-4 no-label box-param-type" data-type='["select", "multiselect", "number"]'>
			<span class="inp-item inp-center">
				{input isProtected: value: 1}
				{label isProtected: /}
			</span>
		</p>
	</div>

	<br>
	<div class="inner">
		<div class="grid-row">
			<span style="position: relative; top: 4px; left: -5px; bottom: 6px;width: 20px; "></span>
			<span class="grid-1-4">
				{_'name'}
			</span>
			<span class="grid-1-4">
				{_'parameters_label_tooltip'}
			</span>
			<span class="grid-1-4">
				{_'parameters_label_filter_prefix'}
			</span>
			<span class="grid-1-5 box-param-type" data-type='["number"]'>
				{_'parameters_label_unit'}
			</span>
		</div>
	</div>

	{foreach $mutations as $mutation}
		<div class="inner">
			<div class="grid-row">
				<span style="position: relative; top: 4px; left: -5px; bottom: 6px;width: 20px; ">{$mutation->langCode|upper}:</span>
				<p class="grid-1-4">
					<span class="inp-fix">
						<input type="text" class="inp-text w-full" name="pname[{$mutation->langCode}]" id="inp-pname" value="{if isset($trParam)}{$trParam[$mutation->langCode]['name']}{/if}"/>
					</span>
				</p>
				<p class="grid-1-4">
					<span class="inp-fix">
						<input type="text" class="inp-text w-full" name="pname_tooltip[{$mutation->langCode}]" id="inp-ptooltip" value="{if isset($trParam)}{$trParam[$mutation->langCode]['tooltip']}{/if}"/>
					</span>
				</p>
				<p class="grid-1-4">
					<span class="inp-fix">
						<input type="text" class="inp-text w-full" name="pname_filter_prefix[{$mutation->langCode}]"  value="{if isset($trParam)}{$trParam[$mutation->langCode]['filterPrefix']}{/if}"/>
					</span>
				</p>
				<p class="grid-1-5 box-param-type" data-type='["number"]'>
					<span class="inp-fix">
						<input type="text" class="inp-text w-full" name="pname_unit[{$mutation->langCode}]" id="inp-punit" value="{if isset($trParam)}{$trParam[$mutation->langCode]['unit']}{/if}"/>
					</span>
				</p>
			</div>
		</div>
	{/foreach}


	{if $parameter->getCfScheme()}
		<div class="menu-tabs">
			<ul class="reset">
				<li><a href="#tab-values">{_tab_values}</a></li>
				<li><a href="#tab-customfields">{_tab_customfields}</a></li>
			</ul>
		</div>
		<div id="tab-content" class="tab-fragment" n:if="$parameter->hasEditableValues()">
			{include 'part/tabs/values.latte'}
		</div>

		<div id="tab-customfields" class="tab-fragment">
			{include 'part/tabs/cf.latte'}
		</div>
	{else}
		{if $parameter->hasEditableValues()}
			{include 'part/tabs/values.latte'}
		{/if}
	{/if}



	<div class="fixed-bar">
		<button class="btn btn-green btn-icon-before">
			<span><span class="icon icon-checkmark"></span> {_'save_button'}</span>
		</button>

		<a n:if="(!$parameter->isProtected)" n:href="delete!" class="btn btn-red btn-icon-before btn-delete ajax">
			<span><span class="icon icon-close"></span> {_'delete_button'}</span>
		</a>
	</div>
{/form}

