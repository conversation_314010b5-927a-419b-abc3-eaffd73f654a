<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Elastic\Components\DataGrid;

use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\EsIndex\EsIndexFacade;
use App\Model\Orm\EsIndex\EsIndexModel;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Orm;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use LogicException;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly ICollection $collection,
		private readonly bool $viewActive,
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly EsIndexFacade $esIndexFacade,
		private readonly EsIndexRepository $esIndexRepository,
		private readonly EsIndexModel $esIndexModel,
		private readonly IndexModel $indexModel,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->collection);
		$grid->addColumnText('id', 'id')->setSortable()->setFilterText();
		$grid->addColumnText('type', 'type')->setSortable()->setFilterText();
		$grid->addColumnText('overview', 'overview')->setRenderer(
			function (EsIndex $esIndex) {
				$parts = [];
				$stats = $this->indexModel->getIndexStats($esIndex);
				if (isset($stats['docs']['count'])) {
					$parts[] = $stats['docs']['count'] . ' items';
				}

				bd($stats);
				if (isset($stats['store']['size_in_bytes'])) {
					$parts[] = $stats['store']['size_in_bytes'] . ' B';
				}

				return implode(' / ', $parts);
			}
		);
		$this->addColumnMutation($grid);
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnText('esName', 'esName');
		$grid->addColumnDateTime('createdTime', 'createdTime')
			->setFormat('Y-m-d H:i:s')
			->setSortable()
			->setFilterDateRange();

		$grid->setTranslator($this->translator);

		if (!$this->viewActive) {
			$grid->addAction('markActive', '', 'markAsActive!')
				->setTitle('Aktivovat')
				->setIcon('check')
				->setConfirmation(
					new CallbackConfirmation(
						function ($item) {
							return 'Opravdu chcete aktivovat index ' . $item->esName . '?';
						}
					)
				);
			$grid->addAction('delete', '', 'delete!')
				->setTitle('Odstranit')
				->setIcon('trash')
				->setConfirmation(
					new CallbackConfirmation(
						function ($item) {
							return 'Opravdu chcete smazat index ' . $item->esName . '?';
						}
					)
				);
		}

		$grid->addAction('fill', '', 'fill!')
			->setTitle('Naplň daty')
			->setIcon('battery-full')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return 'Opravdu si přejete znovu naplnit index ' . $item->esName . '?';
					}
				)
			);

		$grid->addAction('fillDemo', '', 'fillDemo!')
			->setTitle('Naplň demo daty')
			->setIcon('battery-quarter')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return 'Opravdu si přejete naplnit demo daty index ' . $item->esName . '?';
					}
				)
			);

		return $grid;
	}

	public function handleDelete(int $id): void
	{
		$indexToDelete = $this->esIndexRepository->getById([
			'id' => $id,
			'active' => 0,
		]);

		if ($indexToDelete !== null) {
			$this->esIndexFacade->delete($indexToDelete);
		}

		$this->presenter->redirect('this');
	}


	public function handleMarkAsActive(int $id): void
	{
		$esIndex = $this->esIndexRepository->getBy([
			'id' => $id,
			'active' => 0,
		]);

		if ($esIndex !== null) {
			$this->esIndexModel->switchIndex($esIndex);
		}

		$this->presenter->redirect('this');
	}


	public function handleDeleteIndexByName(string $esName): void
	{
		$this->indexModel->deleteByName($esName);
		$this->presenter->redirect('this');
	}


	public function handleFill(int $id, ?int $limit = null): void
	{
		$esIndex = $this->esIndexRepository->getById($id);
		if ($esIndex !== null) {
			try {
				$this->esIndexFacade->fill($esIndex, $limit);
			} catch (LogicException $exception) {
				bd($exception->getTrace());
				$this->flashMessage($exception->getMessage(), 'error');
			}
		} else {
			$this->flashMessage('Index not found');
		}

		$this->presenter->redirect('this');
	}

	public function handleFillDemo(int $id, ?int $limit = null): void
	{
		$this->handleFill($id, 20);
	}

}
