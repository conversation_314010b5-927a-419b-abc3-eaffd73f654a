<?php declare(strict_types=1);

namespace App\Console\Blog;

use App\Model\Orm\Orm;
use App\PostType\Blog\Model\BlogImportService;
use App\PostType\Blog\Model\BlogLocalizationImportParser;
use Nette\Http\FileUpload;
use Nette\Utils\JsonException;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
	name: 'blog:import',
	description: 'Import blogs from JSON file'
)]
class ImportCommand extends Command
{
	public function __construct(
		private readonly BlogImportService $blogImportService,
		private readonly Orm $orm
	)
	{
		parent::__construct();
	}

	protected function execute(InputInterface $input, OutputInterface $output): int
	{

		$this->runCommand($output);

		return self::SUCCESS;
	}


	private function runCommand(OutputInterface $output): void
	{
		$jsonFilePath = __DIR__ . '/import.json';

		try {
			$jsonContent = file_get_contents($jsonFilePath);

			if ($jsonContent === false) {
				$output->writeln('Invalid or inaccessible JSON file.');
				return;
			}

			$blogData = BlogLocalizationImportParser::parseJson($jsonContent);

			$output->writeln('Importing ' . count($blogData) . ' blogs');

			$results = $this->blogImportService->importBlogs(
				$output,
				$blogData,
				$this->orm->mutation->getDefault(),
			);

			$output->writeln(
				sprintf(
					'Import finished. Imported: %d, Skipped: %d, Errors: %d',
					$results['imported'],
					$results['skipped'],
					count($results['errors'])
				)
			);

		} catch (JsonException $e) {
			$output->writeln('Invalid JSON format: ' . $e->getMessage());
		} catch (\InvalidArgumentException $e) {
			$output->writeln('Invalid Data structure: ' . $e->getMessage());
		} catch (\Exception $e) {
			$output->writeln('Error: ' . $e->getMessage());
		}
	}
}
