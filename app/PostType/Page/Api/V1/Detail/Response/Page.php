<?php

declare(strict_types=1);

namespace App\PostType\Page\Api\V1\Detail\Response;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Api\Entity\Response\CustomContent;
use App\Api\Entity\Response\CustomFields;
use App\PostType\Page\Model\Orm\Tree;
use function array_map;

final class Page extends BasicEntity
{

	public readonly int $id;

	public readonly string $template;

	public readonly int|null $commonId;

	public readonly bool $public;

	public readonly string $name;

	public readonly string $nameAnchor;

	public readonly string $nameTitle;

	public readonly string|null $alias;

	public readonly string $annotation;

	public readonly string $content;

	public readonly ?CustomFields $cf;

	public readonly ?CustomContent $cc;

	public readonly ?PageListItem $parent;

	/** @var PageListItem[] */
	public readonly array $children;

	public function __construct(Tree $tree)
	{
		$this->id = $tree->id;
		$this->template = $tree->template;
		$this->commonId = $tree->treeParent?->id;
		$this->public = $tree->public;
		$this->name = $tree->name;
		$this->nameAnchor = $tree->nameAnchor;
		$this->nameTitle = $tree->nameTitle;
		$this->alias = $tree->alias?->alias;
		$this->annotation = $tree->annotation;
		$this->content = $tree->content;
		$this->cf = CustomFields::from($tree->cf);
		$this->cc = CustomContent::from($tree->cc);
		$this->parent = $tree->parent !== null ? new PageListItem($tree->parent) : null;
		$this->children = array_map(
			fn(Tree $child) => new PageListItem($child),
			$tree->crossroad->fetchAll(),
		);
	}

}
