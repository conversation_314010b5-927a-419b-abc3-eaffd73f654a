<?php declare(strict_types = 1);

namespace App\PostType\Author\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Author\Model\Orm\Author;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\BlogTag\Model\BlogTagModel;
use Nextras\Orm\Collection\ICollection;

/**
 * @method Author getObject()
 */
final class AuthorPresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	public function __construct(
		private BlogTagModel $blogTagModel
	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}

	public function renderDefault(CommonTree $object): void
	{
		if ($this->isAjax()) {
			$this->redrawControl('articles');
			$this->redrawControl('tags');
		}
		$this->template->authors = $this->orm->authorLocalization->findBy([
			'mutation' => $this->mutation
		])
			->orderBy(['sort' => ICollection::DESC]);
		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_AUTHOR);
	}


	public function actionDetail(AuthorLocalization $object): void
	{
		$this->setObject($object);
	}


	public function renderDetail(AuthorLocalization $object): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('blog', 'paging');

		$blogs = $object->blogsPublic;

		$paginator->itemCount = $blogs->countStored();

		$this->template->blogs = $blogs->limitBy($paginator->itemsPerPage, $paginator->offset);
		$this->template->author = $object;
		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_AUTHOR);

		$this->template->randomAuthors = $this->orm->authorLocalization->findRandom()
			->findBy($this->orm->authorLocalization->getPublicOnlyWhereParams())
			->findBy([
				'id!=' => $object->id,
				'mutation' => $object->mutation,
			])
			->limitBy(4);
	}


	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
	}

}
