{varType App\PostType\Author\Model\Orm\Author $object}

{block content}
	<div class="row-main row-main--author">
		<div class="u-pt-sm u-mb-last-0 u-mb-xl">
			{default $cf = $object->author->cf->base ?? false}
			{default $name = $seoLink??->name ?? $object->name}
			{default $image = $cf->mainImage ?? false}
			{default $email = $cf->email ?? false}
			{default $phone = $cf->phone ?? false}
			{default $claim = $cf->claim ?? false}
			{default $socials = $cf->socials ?? []}
			{default $annotation = $object->cf??->base??->content ?? $object->cf??->base??->content ?? null}

			<header class="b-annot b-annot--author">
				<div class="b-annot__grid grid grid--x-md@md grid--x-lg@md grid--x-2xl@xl">
					<div class="grid__cell size--5-12@lg u-mb-last-0">
						<div class="u-maw-4-12 u-mb-last-0">
							{php $image = isset($image) ? $image->getEntity() ?? false : false}
							{* Obrázek *}
							{if $image}
								<p class="b-annot__right">
									<img class="img img--3-4" src="{$image->getSize('md-3-4')->src}" alt="" fetchpriority="high">
								</p>
							{/if}
							<div n:if="$claim" class="claim">
								{$claim}
							</div>
						</div>
					</div>
					<div class="grid__cell size--7-12@lg u-mb-last-0">
						<h1 class="b-annot__title u-mb-xs">
							{$name}
						</h1>
						{*výpis tagů na detailu*}
						<p class="b-article__tags u-mb-xs">
							<span n:foreach="$object->authorTags as $tag" class="btn btn--square btn--gray">
								<span class="btn__text">
									{$tag->nameAnchor}
								</span>
							</span>
						</p>
						<p n:if="$email" class="b-annot__contacts u-mb-xs">
							<a href="mailto:{$email}" class="b-annot__btn btn btn--bd"><span class="btn__text u-fw-n">{$email}</span></a>
						</p>
						<p n:if="$phone" class="b-annot__contacts u-mb-xs">
							<a href="tel:{$phone}" class="b-annot__btn btn btn--bd"><span class="btn__text u-fw-n">{$phone}</span></a>
						</p>
						<p n:if="count($socials) > 0" class="b-annot__contacts u-mb-lg">
							{foreach $socials as $item}
								{include $templates . '/part/core/linkChoose.latte', item: $item->link, class: "b-annot__btn btn btn--bd", isButton: true}
							{/foreach}
						</p>
						<div n:ifcontent class="u-mb-last-0 b-annot__desc">
							<p n:if="$annotation">
								{rtrim($annotation ?? '', '<br>')|noescape}
							</p>
							{block annot}{/block}
						</div>
					</div>
				</div>
			</header>

			{include $templates.'/part/crossroad/articles.latte', items: $blogs}
			{* {include $templates.'/part/crossroad/tags.latte', items: $tagsWithCount, titleLang: "title_tags", class: 'u-mb-lg u-mb-xl@lg', linkAllTags=>$pages->aboutUs} *}
			{* {include $templates.'/part/crossroad/authors.latte', items: $randomAuthors, showMore: true, titleLang: "title_authors_other", class: 'u-mb-lg u-mb-xl@lg'} *}
		</div>
	</div>
{/block}
