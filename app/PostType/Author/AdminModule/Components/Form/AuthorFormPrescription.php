<?php declare(strict_types=1);

namespace App\PostType\Author\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Author\AdminModule\Components\Form\FormData\AuthorFormData;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

readonly class AuthorFormPrescription
{
	public function __construct(
		private RelationInfoFactory $relationInfoFactory,
		private Request $request,
		private Builder $coreBuilder,
		private Handler $coreHandler,
		private SuggestUrls $urls,
		private string $coreFormPath,

	)
	{}

	public function get(AuthorLocalization $authorLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addSort($authorLocalization);
		$extenders[] = $this->addTags($authorLocalization);

		$form = new Form();
		$form->setMappedType(AuthorFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTags(AuthorLocalization $authorLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $authorLocalization->getParent(),
			propertyName: 'tags',
			suggestUrl: $this->urls['searchAuthorTag'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, AuthorFormData $data) use ($tagsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);
	}

	public function addSort(AuthorLocalization $authorLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			addHandler: function (Form $form) use ($authorLocalization) {
				$form->addInteger('sort', 'author_sort')->setDefaultValue($authorLocalization->sort ?? 0);
			},
			successHandler: function (Form $form, AuthorFormData $data) use ($authorLocalization) {
				$authorLocalization->sort = $data->sort;
			},
			templateParts: [
				new CommonTemplatePart(__DIR__ . '/parts/side/sort.latte',
					CommonTemplatePart::TYPE_SIDE,
					['sort' => $authorLocalization->sort],
				)
			]);

	}

}
