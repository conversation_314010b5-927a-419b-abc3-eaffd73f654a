<?php declare(strict_types = 1);

namespace App\PostType\Feature\Model\Orm;

use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

class FeatureLocalizationTreeMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'feature_localization_tree';

	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);

		return $conventions;
	}

}
