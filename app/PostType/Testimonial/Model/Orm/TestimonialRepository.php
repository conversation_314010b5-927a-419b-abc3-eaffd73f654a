<?php declare(strict_types = 1);

namespace App\PostType\Testimonial\Model\Orm;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Searchable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Testimonial getById($id)
 * @method Testimonial[]|ICollection searchByName(string $q, array $excluded = [])
 * @method Testimonial[]|ICollection findByExactOrder(array $ids)
 */
final class TestimonialRepository extends Repository implements CollectionById, Searchable
{

	public static function getEntityClassNames(): array
	{
		return [Testimonial::class];
	}


	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

	public function searchByName(string $string, array $excludedIds = []): ICollection
	{
		return $this->getMapper()->searchByName($string, $excludedIds);
	}


	public function getMapper(): TestimonialMapper
	{
		$mapper = parent::getMapper();
		assert($mapper instanceof TestimonialMapper);
		return $mapper;
	}

}
