{varType App\PostType\Reference\Model\Orm\ReferenceLocalization $referenceLocalization}

{if isset($referenceLocalization->firstImage)}
	{php $img = $referenceLocalization->firstImage->getSize('md')}
	{php $stDataImage = $mutation->getBaseUrl().$img->src}
{else}
	{php $stDataImage = NULL}
{/if}


<script type="application/ld+json">
	{
		"@context": "http://schema.org/",
		"@type": "NewsArticle",
		"mainEntityOfPage": {$mutation->getBaseUrlWithPrefix()."/".$referenceLocalization->alias},
		"headline": {$referenceLocalization->name},
		"datePublished": {$referenceLocalization->publicFrom->format('Y-m-d')},
		"dateModified": {if $referenceLocalization->editedTime}{$referenceLocalization->editedTime->format('Y-m-d')}{else}{$referenceLocalization->createdTimeOrder->format('Y-m-d')}{/if},
		"description": {$referenceLocalization->annotation},
		{if $stDataImage}
			"image": {
				"@type": "ImageObject",
				"height": {$stDataImageW},
				"width": {$stDataImageH},
				"url": {$stDataImage}
			},
		{/if}
		"publisher": {
			"@type": "Organization",
			"logo": {
				"@type": "ImageObject",
				"url": {$mutation->getBaseUrl()."/static/img/logo.png"}
			},
			"name": {$publisher}
		},
		"articleBody": {$content}
	}
	</script>
