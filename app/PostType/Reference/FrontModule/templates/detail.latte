{varType App\PostType\Reference\Model\Orm\ReferenceLocalization $object}
{default $cf = $object->reference?->cf?->base?->annot ?? false}

{block content}
	{include $templates.'/part/box/annot.latte', class: 'u-mb-0', cf: $cf}

		<div class="u-mb-last-0 u-pt-md u-pt-lg@md u-mb-lg">
			{control customContentRenderer}
		</div>

	{* {include $templates.'/part/box/contact-person.latte', class: '', hideTitle: true} *}
	{include $templates.'/part/box/contact.latte', title: 'reference_contact_title'}
{/block}

{* <ul n:ifcontent class="u-mb-lg">
	<li n:foreach="$object->categories as $category">
		<a n:href="$category">{$category->name}</a>
	</li>
</ul> *}
{* {include $templates.'/part/crossroad/tags.latte', items: $object->referenceTags} *}
{* <p>
	{_'article_reading_time'}:
	{$object->readingTime} {_($object->readingTime|plural: "minute_1", "minute_2", "minute_3")}
</p> *}
{* {control attachedReferences} *}
