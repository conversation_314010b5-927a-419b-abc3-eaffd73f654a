<?php declare(strict_types=1);

namespace App\PostType\Blog\Model;

use Nette\Utils\Json;
use Nette\Utils\JsonException;

final readonly class BlogLocalizationImportParser
{
	/**
	 * Parse JSON string and return array of blog objects
	 *
	 * @param string $json JSON string containing array of blog objects
	 * @return array Array of parsed blog objects
	 * @throws JsonException When JSON is invalid
	 * @throws \InvalidArgumentException When structure is invalid
	 */
	public static function parseJson(string $json): array
	{
		$data = Json::decode($json, Json::FORCE_ARRAY);

		if (!is_array($data)) {
			throw new \InvalidArgumentException('JSON must contain an array of blog objects');
		}

		$parsedBlogs = [];

		foreach ($data as $index => $item) {
			if (!is_array($item)) {
				throw new \InvalidArgumentException("Item at index {$index} must be an object");
			}

			$parsedBlog = self::validateAndParseItem($item, $index);
			$parsedBlogs[] = $parsedBlog;
		}

		return $parsedBlogs;
	}

	/**
	 * Validate and parse single blog item
	 *
	 * @param array $item Raw blog item data
	 * @param int $index Item index for error reporting
	 * @return array Validated and parsed blog item
	 * @throws \InvalidArgumentException When required fields are missing or invalid
	 */
	private static function validateAndParseItem(array $item, int $index): array
	{
		$requiredFields = ['title', 'content_html', 'slug', 'date'];

		foreach ($requiredFields as $field) {
			if (!isset($item[$field]) || !is_string($item[$field]) || trim($item[$field]) === '') {
				throw new \InvalidArgumentException("Item at index {$index} is missing required field '{$field}' or it's empty");
			}
		}

		// Validate date format
		$date = \DateTime::createFromFormat('Y-m-d', $item['date']);
		if (!$date || $date->format('Y-m-d') !== $item['date']) {
			throw new \InvalidArgumentException("Item at index {$index} has invalid date format. Expected Y-m-d, got: {$item['date']}");
		}

		return [
			'title' => trim($item['title']),
			'content_html' => $item['content_html'],
			'annotation' => isset($item['annotation']) ? trim($item['annotation']) : '',
			'slug' => trim($item['slug']),
			'date' => $item['date'],
			'image' => isset($item['image']) && is_string($item['image']) ? trim($item['image']) : null,
			'tags' => isset($item['tags']) && is_array($item['tags']) ? $item['tags'] : [],
		];
	}
}
