<?php declare(strict_types=1);

namespace App\PostType\Blog\Model\Dto;

use App\Model\Orm\User\User;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

final class BlogLocalizationDto
{

	public function __construct(
		public bool $isPublic,
		public string $name,
		public string $nameAnchor,
		public string $nameTitle,
		public User|null $editor,
		public string|null $alias,
		public ArrayHash|null $cf,
		public ArrayHash|null $cc,
		public ArrayHash|null $commonCf,
		public string $description = '',
		/** @var BlogTagLocalization[] $tags */
		public array $tags = [],
		/** @var AuthorLocalization[] $authors */
		public array $authors = [],
		public ICollection $categories = new EmptyCollection(),
	)
	{
	}




}
