parameters:
	config:
		blog:
			paging: 6 #temp low number - to testing

	postTypeRoutes:
		Blog: blog

cf:
	templates:
		blog:
			base:
				type: group
				items:
					mainImage:
						type: image
						label: "<PERSON><PERSON><PERSON><PERSON> obr<PERSON><PERSON><PERSON> (min. DOPLNIT ROZMĚR)"

		blogLocalization:
			base:
				type: group
				items:
					annotation: @cf.definitions.annotation

cc:
	templates:
		# ":Blog:Front:Blog:detail": []

application:
	mapping:
		Blog: App\PostType\Blog\*Module\Presenters\*Presenter

services:
	- App\PostType\Blog\Model\Orm\BlogLocalizationModel
	- App\PostType\Blog\AdminModule\Components\Form\BlogFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)
	- App\PostType\Blog\Model\BlogLocalizationFacade
	- App\PostType\Blog\Model\BlogImportService
	- App\PostType\Blog\AdminModule\Components\DataGrid\BlogDataGridPrescription
	- App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory

	-
		implement: App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory
		inject: true
