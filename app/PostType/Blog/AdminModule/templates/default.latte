{layout $templates.'/@layout-new.latte'}
{var $dataGridShown = true}

{block #content}
	<div class="main__main">
		<div class="main__header">
			{include $templates.'/part/box/header.latte',
				props: [
					title: Blog,
					isPageTitle: true,
				]
			}
		</div>
		<div class="main__content scroll">
			{control grid}
		</div>
		<div class="main__content-side scroll">
			{control shellForm}
			<div class="b-std u-mb-sm">
				<h2 class="b-std__title title">
					Import
				</h2>
				<div class="b-std__content">


					<p>
						<a n:href="import" class="btn btn--full btn--success">
					<span class="btn__text item-icon">
						<span class="item-icon__text">
							Import z JSON
						</span>
					</span>
						</a>
					</p>
				</div>
			</div>
		</div>
	</div>




