<?php

declare(strict_types=1);

namespace App\PostType\Blog\Api\V1\Create;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestBody;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\Decorator\MutationFinder;
use App\Api\Decorator\RequestAuthentication;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Model\Orm\ApiToken\ApiToken;
use App\PostType\Blog\Api\V1\BlogLocalizationInputData;
use App\PostType\Blog\Api\V1\Detail\Response\BlogLocalization;
use App\PostType\Blog\Model\BlogLocalizationFacade;
use App\PostType\Blog\Model\Dto\BlogLocalizationDto;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use function assert;

#[Path('/')]
#[Tag('Blog')]
final class BlogLocalizationCreateController extends BaseV1Controller
{

	public function __construct(
		private readonly BlogRepository $blogRepository,
		private readonly TreeRepository $treeRepository,
		private readonly BlogLocalizationFacade $blogLocalizationFacade,
	)
	{
	}

	#[Path('/mutation/{mutationId}/blog')]
	#[Method('POST')]
	#[RequestParameter(name: MutationFinder::MUTATION_ID_PARAMETER_NAME, type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation id')]
	#[RequestBody(entity: BlogLocalizationInputData::class, required: true)]
	#[Response(description: 'Success', code: '201', entity: BlogLocalization::class)]
	public function post(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$apiToken = $request->getAttribute(RequestAuthentication::API_TOKEN_ATTRIBUTE_NAME);
		assert($apiToken instanceof ApiToken);

		$inputData = $request->getEntity();
		assert($inputData instanceof BlogLocalizationInputData);

		$blog = $inputData->commonId !== null ? $this->blogRepository->getById($inputData->commonId) : null;

		$mutation = $request->getParameter(MutationFinder::MUTATION_PARAMETER_NAME);
		$blogLocalization = $this->blogLocalizationFacade->create($mutation, $blog);

		$this->blogLocalizationFacade->put($blogLocalization, new BlogLocalizationDto(
			isPublic: $inputData->public,
			name: $inputData->name,
			nameAnchor: $inputData->nameAnchor,
			nameTitle: $inputData->nameTitle,
			editor: $apiToken->issuer,
			alias: $inputData->alias,
			cf: ArrayHash::from($inputData->cf),
			cc: ArrayHash::from($inputData->cc),
			commonCf: ArrayHash::from($inputData->commonCf),
			tags: $inputData->tags,
			authors: $inputData->authors,
			categories: $this->treeRepository->findByIds($inputData->categories),
		));

		return $this->jsonResponse((new BlogLocalization($blogLocalization))->toResponse(), $response)
			->withStatus(ApiResponse::S201_CREATED);
	}

}
