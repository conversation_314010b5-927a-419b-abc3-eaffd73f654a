application:
	mapping:
		MenuMain: App\PostType\MenuMain\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		MenuMain: menu-main

cf:
	definitions:
		customLink:
			hasContentToggle: true
			type: group
			label: "Odkaz"
			items:
				toggle:
					type: radio
					inline: true
					isContentToggle: true
					defaultValue: 'systemHref'
					options: [
						{ label: "Systémová stránka", value: "systemHref" },
						{ label: "Vlastní odkaz", value: "customHref" },
					]
				systemHref:
					type: group
					items:
						page:
							type: suggest
							label: "Stránka"
							subType: tree
							url: @cf.suggestUrls.searchMutationPage
				customHref:
					type: group
					items:
						href:
							type: text
							label: "Odkaz"
		category:
			type: list
			label: "Kategorie"
			items:
				items:
					type: list
					label: "Podkategorie"
					items:
						link:
							extends: @cf.definitions.linkChoose
						icon:
							type: image # has size
							label: "Obrázek nebo SVG (submenu, min. 22x22)"
				desc:
					type: text
					label: "Název kategorie"
	templates:
		menuMain:
			link:
				type: group
				label: 'Odkaz'
				extends: @cf.definitions.customLink

			settings:
				type: group
				label: "Nastavení položky"
				items:
					nonclickable:
						type: checkbox
						label: "Neklikatelná položka (submenu)"

			submenu:
				type: group
				label: "Submenu"
				items:
					categories:
						extends: @cf.definitions.category
services:
	- App\PostType\MenuMain\Model\MenuMainLocalizationFacade
	- App\PostType\MenuMain\AdminModule\Components\MenuMainDataGrid\MenuMainDataGridPrescription
	- App\PostType\MenuMain\AdminModule\Components\MenuMainDetailForm\MenuMainDetailFormPrescription
	- App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalizationModel
