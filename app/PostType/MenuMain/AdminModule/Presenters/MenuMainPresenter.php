<?php declare(strict_types = 1);

namespace App\PostType\MenuMain\AdminModule\Presenters;

use App\AdminModule\Presenters\BasePresenter;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGrid;
use App\PostType\Core\AdminModule\Components\DataGrid\DataGridFactory;
use App\PostType\Core\AdminModule\Components\Form\Form;
use App\PostType\Core\AdminModule\Components\Form\FormFactory;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellForm;
use App\PostType\Core\AdminModule\Components\ShellForm\ShellFormFactory;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\MenuMain\AdminModule\Components\MenuMainDataGrid\MenuMainDataGridPrescription;
use App\PostType\MenuMain\AdminModule\Components\MenuMainDetailForm\MenuMainDetailFormPrescription;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\MenuMain\Model\MenuMainLocalizationFacade;
use Nette\DI\Attributes\Inject;
use Nextras\Orm\Collection\ICollection;

class MenuMainPresenter extends BasePresenter
{

	private const ORM_REPOSITORY_NAME = 'menuMain';

	#[Inject]
	public MenuMainDataGridPrescription $menuMainDataGridPrescription;

	private MenuMainLocalization $menuMainLocalization;

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
		private readonly FormFactory $formFactory,
		private readonly ShellFormFactory $shellFormFactory,
		private readonly MenuMainLocalizationFacade $menuMainLocalizationFacade,
		private readonly MenuMainDetailFormPrescription $menuMainDetailFormDefinition,
	)
	{
		parent::__construct();
	}

	public function renderDefault(): void
	{
	}

	public function actionCreate(): void
	{
		$this->menuMainLocalizationFacade->create($this->mutationHolder->getMutation(), null);

		$this->terminate();
	}

	public function actionEdit(int $id): void
	{
		/**
		 * @var MenuMainLocalization|null $menuMainLocalization
		 */
		$menuMainLocalization = $this->orm->menuMainLocalization->getById($id);
		if ($menuMainLocalization === null) {
			$this->redirect('default');
		}

		$this->menuMainLocalization = $menuMainLocalization;
	}

	public function renderEdit(int $id): void
	{
	}

	public function createComponentShellForm(): ShellForm
	{
		return $this->shellFormFactory->create(entity: null, entityLocalizationFacade: $this->menuMainLocalizationFacade);
	}

	protected function createComponentGrid(): DataGrid
	{
		$collection = $this->orm->menuMainLocalization->findBy([])->orderBy(['menuMain->order' => ICollection::ASC_NULLS_LAST]);

		return $this->dataGridFactory->create(self::ORM_REPOSITORY_NAME, $collection, dataGridDefinition: $this->menuMainDataGridPrescription->get());
	}

	protected function createComponentMenuMainForm(): Form
	{
		return $this->formFactory->create($this->menuMainLocalizationFacade, $this->menuMainLocalization, $this->userEntity, $this->menuMainDetailFormDefinition->getPrescription($this->menuMainLocalization));
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}

}
