<?php

namespace App\PostType\Material\AdminModule\Components\DataGrid;

use App\Model\Translator;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\DataGridDefinition;
use App\PostType\Core\AdminModule\Components\DataGrid\Definition\Extenders\CustomDataGridExtender;
use Ublaboo\DataGrid\DataGrid;

class MaterialDataGridPrescription
{

	public function __construct(
		private readonly Translator $translator,
	)
	{
	}

	public function get(): DataGridDefinition {
		return new DataGridDefinition(
			extenders: [
				new CustomDataGridExtender(
					improveFunction: $this->addIsTop(...)
				),
			]
		);
	}

	private function addIsTop(DataGrid $dataGrid): void
	{
		$dataGrid->addColumnText('isTop', 'isTop')
		->setRenderer(function (MaterialLocalization $materialLocalization) {
			return ($materialLocalization->isTop) ?
				$this->translator->translate('top') :
				$this->translator->translate('common');
		})
		->setFilterSelect([
			true => $this->translator->translate('top'),
			false => $this->translator->translate('common'),
		])->setPrompt($this->translator->translate('all'));
	}
}
