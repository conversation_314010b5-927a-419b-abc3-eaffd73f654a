<?php declare(strict_types=1);

namespace App\PostType\Material\AdminModule\Components\Form;

use App\Model\CustomField\SuggestUrls;
use App\PostType\Material\AdminModule\Components\Form\FormData\MaterialFormData;
use App\PostType\Material\Model\Orm\MaterialLocalization;
use App\PostType\Material\Model\Orm\MaterialLocalizationModel;
use App\PostType\Core\AdminModule\Components\Form\Builder;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\CustomFormExtender;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart;
use App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition;
use App\PostType\Core\AdminModule\Components\Form\Definition\RelationInfoFactory;
use App\PostType\Core\AdminModule\Components\Form\Handler;
use Nette\Application\UI\Form;
use Nette\Http\Request;

class MaterialFormPrescription
{
	public function __construct(
		private readonly MaterialLocalizationModel $materialLocalizationModel,
		private readonly RelationInfoFactory $relationInfoFactory,
		private readonly Request $request,
		private readonly Builder $coreBuilder,
		private readonly Handler $coreHandler,
		private readonly SuggestUrls $urls,
		private readonly string $coreFormPath,

	)
	{}

	public function get(MaterialLocalization $materialLocalization): FormDefinition
	{
		$extenders = [];
		$extenders[] = $this->addTags($materialLocalization);
//		$extenders[] = $this->addAuthors($materialLocalization);
//		$extenders[] = $this->addCategories($materialLocalization);

		$extenders[] = $this->addIsTop($materialLocalization);

		$form = new Form();
		$form->setMappedType(MaterialFormData::class);

		return new FormDefinition(
			form: $form,
			extenders: $extenders,
		);
	}

	public function addTags(MaterialLocalization $materialLocalization): CustomFormExtender
	{
		$tagsRelationsInfo = $this->relationInfoFactory->create(
			sourceEntity: $materialLocalization->getParent(),
			propertyName: 'tags',
			suggestUrl: $this->urls['searchMaterialTag'],
		);

		return new CustomFormExtender(
			addHandler: function (Form $form) use ($tagsRelationsInfo) {
				$this->coreBuilder->addHasManyRelation($form, $tagsRelationsInfo, $this->request->getPost());
			},
			successHandler: function (Form $form, MaterialFormData $data) use ($tagsRelationsInfo) {
				$this->coreHandler->handleHasManyRelation($data->{$tagsRelationsInfo->propertyName}, $tagsRelationsInfo);
			},
			templateParts: [
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/relation.latte',
					type: RelationTemplatePart::TYPE_RELATION),
				new RelationTemplatePart(
					relationInfo: $tagsRelationsInfo,
					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
			]
		);

	}

	public function addIsTop(MaterialLocalization $materialLocalization): CustomFormExtender
	{
		return new CustomFormExtender(
			function (Form $form) use ($materialLocalization) {
				$form->addCheckbox('isTop', 'Top')->setDefaultValue($materialLocalization->isTop);
			},
			function (Form $form, MaterialFormData $data) use ($materialLocalization) {
				$materialLocalization->isTop = $data->isTop;
			},
			[
				new CommonTemplatePart(__DIR__ . '/settings.latte',
					CommonTemplatePart::TYPE_SIDE,
					['langCodeIsTop' => $materialLocalization->mutation->langCode],
				)
			]
		);
	}

//	private function addAuthors(MaterialLocalization $materialLocalization): CustomFormExtender
//	{
//		$authorsRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $materialLocalization->getParent(),
//			propertyName: 'authors',
//			suggestUrl: $this->urls['searchAuthors'],
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($authorsRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $authorsRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, MaterialFormData $data) use ($authorsRelationsInfo) {
//				$this->coreHandler->handleHasManyRelation($data->{$authorsRelationsInfo->propertyName}, $authorsRelationsInfo);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $authorsRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}

//	private function addCategories(MaterialLocalization $materialLocalization): CustomFormExtender
//	{
//		$url = $this->urls['searchMutationPage'];
//		$url->params['mutationId'] = $materialLocalization->mutation->id;
//		$url->params['templates'] = [':Material:Front:Material:default'];
//
//		$categoriesRelationsInfo = $this->relationInfoFactory->create(
//			sourceEntity: $materialLocalization,
//			propertyName: 'materialLocalizationTrees',
//			suggestUrl: $url,
//			inputSuggestPropertyName: 'name',
//			toggleName: 'categories',
//			dragAndDrop: true,
//			builderCollection: $materialLocalization->categories,
//
//		);
//
//		return new CustomFormExtender(
//			addHandler: function (Form $form) use ($categoriesRelationsInfo) {
//				$this->coreBuilder->addHasManyRelation($form, $categoriesRelationsInfo, $this->request->getPost());
//			},
//			successHandler: function (Form $form, MaterialFormData $data) use ($categoriesRelationsInfo, $materialLocalization) {
//				$ids = Handler::readOnlyValidIds($data->{$categoriesRelationsInfo->propertyName});
//				$this->materialLocalizationModel->setCategoriesByIds($materialLocalization, $ids);
//			},
//			templateParts: [
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile: $this->coreFormPath . '/parts/relation.latte',
//					type: RelationTemplatePart::TYPE_RELATION),
//				new RelationTemplatePart(
//					relationInfo: $categoriesRelationsInfo,
//					templateFile:$this->coreFormPath . '/parts/newItemTemplate.latte',
//					type: RelationTemplatePart::TYPE_RELATION_PRESCRIPTION),
//			]
//		);
//	}
}
