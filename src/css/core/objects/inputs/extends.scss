@use 'base/variables';

%inp {
	display: block;
	width: 100%;
	padding: 0 0 0.4rem;
	border: 0.1rem solid variables.$color-text;
	border-width: 0 0 0.1rem;
	border-radius: 0;
	background: transparent;
	color: variables.$color-text;
	outline: none;
	font-size: variables.$font-size;
	line-height: variables.$line-height;
	appearance: none;
	transition: background-color variables.$t, border-color variables.$t, outline-color variables.$t;

	// hide number arrows
	&::-webkit-outer-spin-button,
	&::-webkit-inner-spin-button {
		margin: 0;
		-webkit-appearance: none;
	}
	&[type='number'] {
		-moz-appearance: textfield;
	}

	// STATEs
	&:disabled {
		border-color: variables.$color-gray;
		opacity: 0.5;
	}

	&:focus,
	&:focus-within {
		border-color: variables.$color-gray;
	}
	.has-error & {
		border-color: variables.$color-red;
	}
}
