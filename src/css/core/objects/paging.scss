@use 'base/extends';
@use 'base/variables';

.paging {
	&__list {
		@extend %reset-ul;
		display: flex;
		justify-content: center;
		margin: 0 0 -1rem -1rem;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 0 0 1rem 1rem;
		padding: 0.8rem 1.4rem;
		border: 0.1rem solid variables.$color-btn;
		border-radius: variables.$border-radius;
		background-color: transparent;
		color: variables.$color-btn;
		line-height: 100%;
		text-align: center;
		text-decoration: none;
		transition: background-color variables.$t, border-color variables.$t, color variables.$t;
	}
}
