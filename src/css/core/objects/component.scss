@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.component {
	position: relative;
	margin: functions.spacing('2xl') 0;
	&::before {
		content: '';
		position: absolute;
		inset: -4rem;
		border: 0.1rem dashed variables.$color-bd;
		border-radius: 10px;
		pointer-events: none;
	}
	&__title {
		position: absolute;
		top: -4.8rem;
		left: 1rem;
		padding: 0 1rem;
		background: variables.$color-white;
	}
}
