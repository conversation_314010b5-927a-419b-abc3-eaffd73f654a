@use 'base/variables';
@use 'config';

.header {
	--row-main-width: calc(144.8rem + 2 * var(--row-main-gutter));
	$s: &;
	position: sticky;
	top: 0;
	z-index: 100;
	background: #f7f7f7;
	&__wrap {
		display: flex;
		gap: 1rem;
		justify-content: space-between;
		align-items: center;
		height: var(--header-height);
	}
	&__logo {
		position: relative;
		flex: 0 0 auto;
		justify-content: center;
		align-self: center;
		width: 219px;
		height: 40px;
		font-size: 0;
	}
	&__link:hover {
		#{$s}__svg {
			opacity: 0;
			&--hover {
				opacity: 1;
			}
		}
	}
	&__svg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		transition: opacity 0.3s ease;
		transition: opacity variables.$t;
		&--hover {
			opacity: 0;
		}
	}
	&__search {
		flex: 0;
		margin-left: auto;
	}
	&__other {
		display: flex;
	}
	&__burger {
		display: flex;
	}
	&--shadow {
		box-shadow: 0 4px 11px 0 #0000001a;
	}

	// MQ
	@media (config.$ls-down) {
		&__menu {
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			flex-direction: column;
			padding: var(--row-main-gutter);
			background: variables.$color-white;
			text-align: center;
			opacity: 0;
			visibility: hidden;
			&-bg {
				position: fixed;
				top: var(--header-height);
				left: 0;
				z-index: -1;
				background-color: rgba(variables.$color-black, 0.5);
				opacity: 0;
				visibility: hidden;
				transition: opacity variables.$t, visibility variables.$t;
			}
		}
		&__wrap.is-open &__menu-bg {
			right: 0;
			bottom: 0;
			opacity: 1;
			visibility: visible;
		}

		// STATES
		body:has(&__wrap.is-open) {
			overflow: clip;
		}
		.is-open &__menu {
			max-height: calc(100dvh - var(--header-height));
			overflow-y: auto;
			opacity: 1;
			visibility: visible;
		}
	}
	@media (config.$xs-down) {
		&__logo {
			width: 18rem;
		}
	}
	@media (config.$ls-up) {
		&__burger {
			display: none;
		}
		&__menu {
			display: flex;
			flex: 1 1 auto;
			gap: 1rem;
			align-items: center;
		}
		&__search {
			order: 1;
		}
	}
	@media (config.$xl-up) {
		&__logo {
			width: 236px;
		}
		&__menu {
			gap: 2rem;
			max-width: calc(100% - 236px - 1rem);
		}
	}
}
