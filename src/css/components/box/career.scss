@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-career {
	margin-bottom: 5rem;
	padding-top: 5rem;

	&__title {
		max-width: 70rem;
		margin: 0 auto 1.2rem;
		text-align: center;
	}

	&__desc {
		margin: 0 0 1.8rem;
	}

	&__images {
		display: flex;
		gap: 2.3rem;
		max-width: 107rem;
		margin: 0 auto;
	}

	&__image {
		flex: 1;
		max-height: 42rem;
		.img {
			width: 100%;
			height: 100%;
			border-radius: 0.8rem;
			background-color: variables.$color-gray10;
			object-fit: cover;
			overflow: hidden;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__images {
			flex-wrap: wrap;
			gap: 1.4rem;
			padding-bottom: 2.4rem;
		}

		&__image {
			flex: 1 1 calc(50% - 0.7rem);
			max-width: calc(50% - 0.7rem);
			&:nth-child(2) .img,
			&:nth-child(4) .img {
				margin-top: 2.4rem;
				background-color: variables.$color-gray10;
			}
		}
	}
	@media (config.$md-up) {
		margin-bottom: 10rem;
		padding-top: 10rem;
		&__title {
			margin-bottom: 2.4rem;
		}
		&__desc {
			margin: 0 0 3.2rem;
		}
		&__images {
			padding-bottom: 3.8rem;
		}
		&__image {
			&:nth-child(2) .img {
				margin-top: 3.8rem;
			}

			&:nth-child(3) .img {
				margin-top: 1.8rem;
			}
		}
	}
}
