@use 'base/variables';
@use 'config';

.f-search {
	margin: 0 0 0 auto;
	.inp-icon__icon {
		.icon-svg {
			transition: opacity variables.$t;
		}
	}
	&__wrap {
		position: relative;
		display: flex;
		max-width: variables.$row-main-width-content;
		height: 6.4rem;
		margin: 0 auto;
		border-radius: variables.$border-radius;
		background-color: variables.$color-primary;
		transition: height variables.$t;
		box-shadow: 0 0 0.4rem 0 rgba(0, 0, 0, 0.25);
		.inp-fix {
			flex: 1;
		}
	}
	&__loader {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 1.6rem;
		height: 1.6rem;
		margin: -0.8rem 0 0 -0.8rem;
		border: 0.2rem solid;
		border-top-color: transparent;
		border-radius: 2rem;
		opacity: 0;
		transition: opacity variables.$t;
	}

	&__inp {
		height: 100%;
		padding: 1.6rem 6.4rem 1.6rem 1.6rem;
		border: none;
		font-size: 32px;
		line-height: 38px;
	}

	&__btn {
		position: absolute;
		right: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 6.4rem;
		height: 100%;
		.btn__text {
			border: none;
		}
		.btn__icon {
			width: 3.2rem;
			height: 3.2rem;
			color: variables.$color-text;
		}
	}

	// STATEs
	&--active &__inp {
		border-color: variables.$color-primary;
	}
	&__inp.is-loading ~ .inp-icon__icon {
		.icon-svg {
			opacity: 0;
		}
		.f-search__loader {
			opacity: 1;
			animation: animation-rotate 0.8s infinite linear;
		}
	}

	@media (config.$md-up) {
		&__wrap {
			height: 8.6rem;
		}
		&__btn {
			width: 8.6rem;
		}
		&__inp {
			padding-right: 8.6rem;
		}
	}
}
