// Box
@forward 'components/box/annot';
@forward 'components/box/animation';
@forward 'components/box/article';
@forward 'components/box/author';
@forward 'components/box/awards';
@forward 'components/box/branch';
@forward 'components/box/burger';
@forward 'components/box/card';
@forward 'components/box/cookie';
@forward 'components/box/faq';
@forward 'components/box/feature';
@forward 'components/box/career';
@forward 'components/box/suggest';
@forward 'components/box/content';
@forward 'components/box/support';
// @forward 'components/box/modal';
@forward 'components/box/result';
@forward 'components/box/highlights';
@forward 'components/box/annot-blog';
@forward 'components/box/branches-map';
@forward 'components/box/count';
@forward 'components/box/casestudy';
@forward 'components/box/logos';
@forward 'components/box/footer-branch';
@forward 'components/box/contact-person';
@forward 'components/box/contact';
@forward 'components/box/partners';
@forward 'components/box/share';
@forward 'components/box/tabs';
@forward 'components/box/testimonial';

// Crossroad
@forward 'components/crossroad/authors';
@forward 'components/crossroad/cards';
@forward 'components/crossroad/casestudies';
@forward 'components/crossroad/partners';
@forward 'components/crossroad/tags';

// Form
@forward 'components/form/contact';
@forward 'components/form/search';

// Menu
@forward 'components/menu/accessibility';
@forward 'components/menu/main';
@forward 'components/menu/submenu';
@forward 'components/menu/lang';
@forward 'components/menu/footer';
// @forward 'components/menu/breadcrumb';
