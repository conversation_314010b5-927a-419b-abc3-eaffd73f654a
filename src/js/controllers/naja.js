import { Controller } from '@hotwired/stimulus';
import naja from 'naja';
import netteForms from 'nette-forms';
import { createModal } from '@superkoders/modal';
import { defaultOptions } from './modal';
import { getOffsetTop } from '../tools/getOffsetTop.js';
import { getExtraOffset } from '../tools/getExtraOffset.js';
export const create = () => {
	return class extends Controller {
		connect() {
			let currentTarget = null;
			naja.uiHandler.selector = '[data-naja]';

			naja.uiHandler.addEventListener('interaction', (event) => {
				// unification of current target
				currentTarget = event.detail.element;
				if (!currentTarget.dataset.naja) {
					currentTarget = currentTarget.closest('[data-naja]');
				}

				// Validace - pokud existuji (viditelne) inp-validate a mají chybu, tak nepokracuj
				if (this.checkErrors(currentTarget)) {
					event.preventDefault();
					return false;
				}

				// fix for aborting ajaxes (interaction will start afrter complete)
				setTimeout(() => {
					// add loading state
					if (currentTarget.dataset.najaLoader) {
						document.querySelector(currentTarget.dataset.najaLoader).classList.add('is-loading');
					} else {
						currentTarget.classList.add('is-loading');
					}

					// scroll after ajax
					this.scrollTo(currentTarget.dataset.najaScroll);

					// modal create
					if (currentTarget.dataset.najaModal) {
						// close other modal
						const modalClose = document.querySelector('.b-modal__close');
						if (modalClose) document.querySelector('.b-modal__close').click();

						if (typeof currentTarget.dataset.najaModalTarget === 'undefined') {
							const modal = createModal(
								{
									url: `<div id="${currentTarget.dataset.najaModal}" />`,
									medium: 'content',
								},
								{ customWrapperClass: currentTarget.dataset.customClass, ...defaultOptions },
							);
							modal.methods.open();
						}
					}
				});
			});

			naja.addEventListener('complete', (event) => {
				let modalTarget = null;

				// Otevření v modalu
				if (currentTarget && event.detail.payload && (modalTarget = event.detail.payload.modalTarget)) {
					event.detail.options.test = 'test';
					const options = {
						fetch: {
							headers: {
								'Content-Type': 'application/json',
							},
						},
						history: false,
					};

					naja.makeRequest('GET', modalTarget, null, options).then((payload) => {
						let content = 'Error: No valid snippet id provided.';
						if (currentTarget.dataset.najaModal && payload.snippets && payload.snippets[currentTarget.dataset.najaModal]) {
							content = `<div id="${currentTarget.dataset.najaModal}">${
								payload.snippets[currentTarget.dataset.najaModal]
							}</div>`;
						}

						const modal = createModal(
							{
								url: content,
								medium: 'content',
							},
							{ customWrapperClass: currentTarget.dataset.customClass, ...defaultOptions },
						);
						modal.methods.open();

						this.removeLoader(currentTarget);
					});
				} else {
					this.removeLoader(currentTarget);
				}

				// Změna URL
				if (event?.detail?.payload?.newUrl) {
					naja.historyHandler.historyAdapter.pushState([], event.detail.payload.newUrl);
				}
			});

			// Validace
			naja.formsHandler.netteForms = netteForms;
			naja.formsHandler.netteForms.validateForm = function () {
				return true;
			};

			naja.initialize();

			// Validace dle data-nette-rules
			['focusout', 'change', 'paste'].forEach((e) => {
				document.addEventListener(e, (event) => {
					var input = event.target;
					if (input.classList.contains('inp-validate')) {
						setTimeout(() => {
							this.validateInput(input);
						}, 0);
					}
				});
			});
		}

		// Validace
		validateInput = (input) => {
			if (naja.formsHandler.netteForms.validateControl(input, null, false)) {
				// bez chyby (pokud ma hodnotu - workaround pro skryte elementy, ktere projdou do teto vetve, ale jsou prazdne - nevalidovat)
				if (input.value) {
					input.parentNode.classList.add('is-ok');
				}
				input.parentNode.classList.remove('has-error');
				if (input.parentNode.querySelector('.inp-error')) {
					input.parentNode.querySelector('.inp-error').textContent = '';
					input.parentNode.querySelector('.inp-error').classList.add('u-d-n');
				}
			} else {
				// s chybou
				let inputError = '';

				naja.formsHandler.netteForms.formErrors.forEach((error) => {
					if (error.element === input) {
						inputError = error;
					}
				});

				input.parentNode.classList.remove('is-ok');
				input.parentNode.classList.add('has-error');
				if (input.parentNode.querySelector('.inp-error')) {
					input.parentNode.querySelector('.inp-error').textContent = inputError.message;
					input.parentNode.querySelector('.inp-error').classList.remove('u-d-n');
				}
				// console.log(naja.formsHandler.netteForms.formErrors, inputError, inputError.message);
			}
		};

		// Validace
		checkErrors = (currentTarget) => {
			var inputs = currentTarget.querySelectorAll('.inp-validate');

			if (inputs.length) {
				// Zvaliduj všechny inputy v currentTarget
				inputs.forEach((input) => this.validateInput(input));

				// Pokud jsou zde error, zascrolluj na první a zastav Naju
				var errorInput = currentTarget.querySelector('.has-error .inp-validate');
				if (errorInput) {
					errorInput.focus();
					return true;
				} else {
					return false;
				}
			} else {
				return false;
			}
		};

		scrollTo = (element) => {
			if (element && document.querySelector(element)) {
				var scrollTarget = document.querySelector(element);
				var elOffset = getOffsetTop(scrollTarget);

				if (window.scrollY > elOffset) {
					window.scrollTo({
						behavior: 'smooth',
						left: 0,
						top: elOffset - getExtraOffset(),
					});
				}
			}
		};

		removeLoader = (currentTarget) => {
			if (currentTarget.dataset.najaLoader) {
				document.querySelector(currentTarget.dataset.najaLoader).classList.remove('is-loading');
			} else {
				currentTarget.classList.remove('is-loading');
			}
		};
	};
};
